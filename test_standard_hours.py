#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试标准工时管理器功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from standard_hours_manager import StandardHoursManager
    print("✅ 成功导入标准工时管理器")
    
    # 创建管理器实例
    manager = StandardHoursManager()
    print("✅ 成功创建标准工时管理器实例")
    
    # 测试获取标准工时
    test_contents = [
        "JH011-SH完成品",
        "JH011-SH空箱", 
        "UF009完成品",
        "不存在的作业内容"
    ]
    
    print("\n📋 测试获取标准工时:")
    for content in test_contents:
        hours = manager.get_standard_hours(content)
        if hours:
            print(f"  {content}: {hours}分钟 ({hours/60:.1f}小时)")
        else:
            print(f"  {content}: 未找到标准工时")
    
    # 测试保存配置
    print("\n💾 测试保存配置...")
    manager.save_config()
    print("✅ 配置保存成功")
    
    print("\n🎉 所有测试通过！")
    
except ImportError as e:
    print(f"❌ 导入失败: {e}")
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
