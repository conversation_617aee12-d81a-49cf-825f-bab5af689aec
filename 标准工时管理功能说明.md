# ⏱️ 标准工时管理功能说明

## 🎯 功能概述

标准工时管理功能是工作计划排程系统的新增功能，允许用户灵活配置和管理不同作业内容的标准工时，用于实际工时分析中的超时计算。

## 🚀 主要特性

### 1. **灵活配置**
- 支持添加、编辑、删除标准工时配置
- 支持从Excel文件导入标准工时
- 支持导出标准工时到Excel文件
- 支持重置为默认配置

### 2. **智能匹配**
- 精确匹配作业内容
- 模糊匹配功能（当精确匹配失败时）
- 自动保存配置更改

### 3. **界面友好**
- 直观的表格显示
- 便捷的工具栏操作
- 实时状态更新

## 📋 使用方法

### 1. 打开标准工时管理
有两种方式打开标准工时管理界面：

**方式一：通过菜单**
1. 运行工作计划排程系统
2. 点击菜单栏 "🔧 操作" → "⏱️ 标准工时管理"

**方式二：通过汇总计划处理页面**
1. 点击主界面的 "📋 汇总计划处理" 按钮
2. 在汇总计划处理页面中，点击 "⏱️ 标准工时管理" 按钮

### 2. 管理标准工时

#### ➕ 添加标准工时
1. 点击工具栏的 "➕ 添加" 按钮
2. 在弹出的对话框中输入：
   - **作业内容**：如 "JH011-SH完成品"
   - **标准工时**：可以用小时数（如 2.5）或时:分格式（如 2:30）
3. 点击 "保存" 按钮

#### ✏️ 编辑标准工时
1. 在表格中选择要编辑的项目
2. 点击工具栏的 "✏️ 编辑" 按钮，或双击表格行
3. 修改作业内容或标准工时
4. 点击 "保存" 按钮

#### 🗑️ 删除标准工时
1. 在表格中选择要删除的项目
2. 点击工具栏的 "🗑️ 删除" 按钮
3. 确认删除操作

#### 📁 从Excel导入
1. 点击工具栏的 "📁 从Excel导入" 按钮
2. 选择包含标准工时数据的Excel文件
3. 文件格式要求：
   - 第一列：作业内容
   - 第二列：标准工时（支持 HH:MM:SS 格式）

#### 💾 导出到Excel
1. 点击工具栏的 "💾 导出到Excel" 按钮
2. 选择保存位置和文件名
3. 系统会生成包含所有标准工时配置的Excel文件

#### 🔄 重置为默认
1. 点击工具栏的 "🔄 重置为默认" 按钮
2. 确认重置操作
3. 系统会恢复到默认的标准工时配置

## 🔧 与实际工时分析的集成

### 自动应用标准工时
当进行实际工时分析时，系统会：

1. **优先使用标准工时管理器**：根据作业内容自动匹配标准工时
2. **备用Excel数据**：如果标准工时管理器中没有找到，则使用Excel文件中的"作业时长"列
3. **智能匹配**：支持精确匹配和模糊匹配

### 超时计算逻辑
```
超作业标准工时(分) = 实际作业时长 - 标准工时
```
- 正值表示超时
- 负值表示提前完成

## 📊 数据格式

### 支持的时间格式
- **小时数**：2.5（表示2小时30分钟）
- **时:分格式**：2:30
- **Excel时间格式**：02:30:00

### 配置文件
- **JSON格式**：`standard_hours_config.json`
- **自动保存**：每次修改后自动保存
- **版本信息**：包含最后更新时间

## 💡 使用建议

### 1. **初始设置**
- 首次使用时，建议从现有的"作业标准工时.xlsx"文件导入数据
- 根据实际情况调整标准工时

### 2. **定期维护**
- 根据作业效率变化定期更新标准工时
- 为新的作业内容添加标准工时配置

### 3. **备份管理**
- 定期导出标准工时配置作为备份
- 重要更改前建议先导出当前配置

## ⚠️ 注意事项

1. **作业内容匹配**：确保作业内容名称与实际数据中的一致
2. **时间格式**：标准工时必须大于0
3. **配置保存**：所有更改会自动保存到配置文件
4. **权限要求**：确保程序有读写配置文件的权限

## 🔍 故障排除

### 常见问题

**Q: 标准工时管理器无法打开？**
A: 检查是否正确安装了所有依赖包，特别是tkinter和pandas。

**Q: 导入Excel文件失败？**
A: 确保Excel文件格式正确，第一列为作业内容，第二列为标准工时。

**Q: 实际工时分析时没有使用标准工时管理器的数据？**
A: 检查作业内容名称是否匹配，系统会优先使用精确匹配，然后尝试模糊匹配。

**Q: 配置文件丢失？**
A: 系统会自动创建默认配置，也可以从Excel文件重新导入数据。

---

## 📈 版本历史

- **v1.0.0** - 初始版本，支持基本的标准工时管理功能
- **v1.0.1** - 增加模糊匹配功能
- **v1.0.2** - 优化界面和用户体验

---

*如有问题或建议，请联系系统管理员。*
