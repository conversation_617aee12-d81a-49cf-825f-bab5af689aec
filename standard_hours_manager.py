#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标准工时管理模块
提供标准工时的配置、编辑和管理功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import json
import os
import logging
from datetime import datetime
from typing import Dict, List, Optional


class StandardHoursManager:
    """标准工时管理器"""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.config_file = "standard_hours_config.json"
        self.default_excel_file = "作业标准工时.xlsx"
        self.standard_hours_data = {}
        self.window = None
        
        # 加载现有配置
        self.load_config()
    
    def load_config(self):
        """加载标准工时配置"""
        try:
            # 首先尝试从JSON配置文件加载
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    # 处理新格式的配置文件
                    if isinstance(config_data, dict) and 'standard_hours' in config_data:
                        self.standard_hours_data = config_data['standard_hours']
                    else:
                        # 兼容旧格式
                        self.standard_hours_data = config_data
                logging.info(f"从配置文件加载标准工时: {len(self.standard_hours_data)} 项")
            
            # 如果配置文件为空或不存在，尝试从Excel文件加载
            elif os.path.exists(self.default_excel_file):
                self.load_from_excel(self.default_excel_file)
                logging.info(f"从Excel文件加载标准工时: {len(self.standard_hours_data)} 项")
            
            # 如果都没有，使用默认配置
            else:
                self.create_default_config()
                logging.info("使用默认标准工时配置")
                
        except Exception as e:
            logging.error(f"加载标准工时配置失败: {str(e)}")
            self.create_default_config()
    
    def load_from_excel(self, excel_file):
        """从Excel文件加载标准工时配置"""
        try:
            df = pd.read_excel(excel_file)
            self.standard_hours_data = {}
            
            for _, row in df.iterrows():
                content = str(row.iloc[0]).strip()  # 作业内容
                duration_str = str(row.iloc[1]).strip()  # 标准工时
                
                # 解析时长字符串转换为分钟数
                minutes = self.parse_duration_to_minutes(duration_str)
                if minutes > 0:
                    self.standard_hours_data[content] = minutes
            
            # 保存到JSON配置文件
            self.save_config()
            
        except Exception as e:
            logging.error(f"从Excel文件加载标准工时失败: {str(e)}")
            raise
    
    def create_default_config(self):
        """创建默认的标准工时配置"""
        self.standard_hours_data = {
            "JH011-SH完成品": 120,  # 2小时
            "JH011-SH空箱": 60,     # 1小时
            "UF009完成品": 150,     # 2.5小时
            "JH027-SD待检品": 120,  # 2小时
            "JH027-SC待检品": 120,  # 2小时
            "JH027-SC周转箱": 120,  # 2小时
            "JH027-SD周转箱": 120,  # 2小时
            "JT026-24周转箱": 120,  # 2小时
            "JT026-24电池": 150,    # 2.5小时
            "JH027-SB电池": 150,    # 2.5小时
        }
        self.save_config()
    
    def parse_duration_to_minutes(self, duration_str):
        """将时长字符串转换为分钟数"""
        try:
            if pd.isna(duration_str) or not duration_str:
                return 0
            
            duration_str = str(duration_str).strip()
            
            # 处理 HH:MM:SS 格式
            if ':' in duration_str:
                parts = duration_str.split(':')
                if len(parts) >= 2:
                    hours = int(parts[0])
                    minutes = int(parts[1])
                    return hours * 60 + minutes
            
            # 处理纯数字（假设为小时）
            try:
                hours = float(duration_str)
                return int(hours * 60)
            except:
                pass
            
            return 0
            
        except Exception as e:
            logging.warning(f"解析时长失败: {duration_str}, 错误: {str(e)}")
            return 0
    
    def save_config(self):
        """保存标准工时配置到JSON文件"""
        try:
            config_data = {
                "last_updated": datetime.now().isoformat(),
                "standard_hours": self.standard_hours_data
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            
            logging.info(f"标准工时配置已保存: {len(self.standard_hours_data)} 项")
            
        except Exception as e:
            logging.error(f"保存标准工时配置失败: {str(e)}")
            raise
    
    def get_standard_hours(self, content):
        """根据作业内容获取标准工时（分钟）"""
        if not content:
            return None
        
        content = str(content).strip()
        
        # 精确匹配
        if content in self.standard_hours_data:
            return self.standard_hours_data[content]
        
        # 模糊匹配
        content_lower = content.lower()
        for key, value in self.standard_hours_data.items():
            if key.lower() in content_lower or content_lower in key.lower():
                return value
        
        return None
    
    def show_manager_window(self):
        """显示标准工时管理窗口"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return
        
        self.window = tk.Toplevel(self.parent) if self.parent else tk.Tk()
        self.window.title("标准工时管理")
        self.window.geometry("800x600")
        self.window.resizable(True, True)
        
        # 创建界面
        self.create_manager_interface()
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """将窗口居中显示"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_manager_interface(self):
        """创建管理界面"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="标准工时管理", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 工具栏
        toolbar_frame = ttk.Frame(main_frame)
        toolbar_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 工具栏按钮
        ttk.Button(toolbar_frame, text="➕ 添加", command=self.add_standard_hour).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="✏️ 编辑", command=self.edit_standard_hour).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="🗑️ 删除", command=self.delete_standard_hour).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="📁 从Excel导入", command=self.import_from_excel).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="💾 导出到Excel", command=self.export_to_excel).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar_frame, text="🔄 重置为默认", command=self.reset_to_default).pack(side=tk.LEFT, padx=(0, 5))
        
        # 数据表格
        self.create_data_table(main_frame)
        
        # 状态栏
        self.status_var = tk.StringVar()
        status_label = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_label.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 更新状态
        self.update_status()
        
        # 加载数据
        self.refresh_table()

    def create_data_table(self, parent):
        """创建数据表格"""
        # 表格框架
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # 创建Treeview
        columns = ("作业内容", "标准工时(分钟)", "标准工时(时:分)")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        self.tree.heading("作业内容", text="作业内容")
        self.tree.heading("标准工时(分钟)", text="标准工时(分钟)")
        self.tree.heading("标准工时(时:分)", text="标准工时(时:分)")

        # 设置列宽
        self.tree.column("作业内容", width=300, minwidth=200)
        self.tree.column("标准工时(分钟)", width=120, minwidth=100)
        self.tree.column("标准工时(时:分)", width=120, minwidth=100)

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 布局
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # 绑定双击事件
        self.tree.bind("<Double-1>", lambda e: self.edit_standard_hour())

    def refresh_table(self):
        """刷新表格数据"""
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)

        # 添加数据
        for content, minutes in sorted(self.standard_hours_data.items()):
            hours_str = self.minutes_to_hours_str(minutes)
            self.tree.insert("", tk.END, values=(content, minutes, hours_str))

        self.update_status()

    def minutes_to_hours_str(self, minutes):
        """将分钟数转换为时:分格式"""
        hours = minutes // 60
        mins = minutes % 60
        return f"{hours}:{mins:02d}"

    def update_status(self):
        """更新状态栏"""
        count = len(self.standard_hours_data)
        self.status_var.set(f"共 {count} 项标准工时配置")

    def add_standard_hour(self):
        """添加标准工时"""
        self.show_edit_dialog()

    def edit_standard_hour(self):
        """编辑标准工时"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要编辑的项目")
            return

        item = selection[0]
        values = self.tree.item(item, "values")
        content = values[0]
        minutes = int(values[1])

        self.show_edit_dialog(content, minutes)

    def delete_standard_hour(self):
        """删除标准工时"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的项目")
            return

        item = selection[0]
        values = self.tree.item(item, "values")
        content = values[0]

        if messagebox.askyesno("确认删除", f"确定要删除 '{content}' 的标准工时配置吗？"):
            del self.standard_hours_data[content]
            self.save_config()
            self.refresh_table()
            messagebox.showinfo("成功", "删除成功")

    def show_edit_dialog(self, content="", minutes=0):
        """显示编辑对话框"""
        dialog = tk.Toplevel(self.window)
        dialog.title("编辑标准工时" if content else "添加标准工时")
        dialog.geometry("400x200")
        dialog.resizable(False, False)
        dialog.transient(self.window)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"400x200+{x}+{y}")

        # 主框架
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 作业内容
        ttk.Label(main_frame, text="作业内容:").grid(row=0, column=0, sticky=tk.W, pady=(0, 10))
        content_var = tk.StringVar(value=content)
        content_entry = ttk.Entry(main_frame, textvariable=content_var, width=30)
        content_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 10))

        # 标准工时（小时）
        ttk.Label(main_frame, text="标准工时(小时):").grid(row=1, column=0, sticky=tk.W, pady=(0, 10))
        hours_var = tk.StringVar(value=str(minutes / 60) if minutes > 0 else "")
        hours_entry = ttk.Entry(main_frame, textvariable=hours_var, width=30)
        hours_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 10))

        # 或者用时:分格式
        ttk.Label(main_frame, text="或时:分格式:").grid(row=2, column=0, sticky=tk.W, pady=(0, 10))
        time_var = tk.StringVar(value=self.minutes_to_hours_str(minutes) if minutes > 0 else "")
        time_entry = ttk.Entry(main_frame, textvariable=time_var, width=30)
        time_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 10))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(20, 0))

        def save_data():
            new_content = content_var.get().strip()
            if not new_content:
                messagebox.showerror("错误", "作业内容不能为空")
                return

            # 尝试解析工时
            new_minutes = 0
            hours_str = hours_var.get().strip()
            time_str = time_var.get().strip()

            if hours_str:
                try:
                    hours = float(hours_str)
                    new_minutes = int(hours * 60)
                except ValueError:
                    messagebox.showerror("错误", "小时格式不正确")
                    return
            elif time_str:
                try:
                    if ':' in time_str:
                        parts = time_str.split(':')
                        h = int(parts[0])
                        m = int(parts[1])
                        new_minutes = h * 60 + m
                    else:
                        new_minutes = int(float(time_str) * 60)
                except ValueError:
                    messagebox.showerror("错误", "时间格式不正确")
                    return
            else:
                messagebox.showerror("错误", "请输入标准工时")
                return

            if new_minutes <= 0:
                messagebox.showerror("错误", "标准工时必须大于0")
                return

            # 检查是否重复（编辑时排除自己）
            if new_content != content and new_content in self.standard_hours_data:
                messagebox.showerror("错误", "该作业内容已存在")
                return

            # 删除旧的（如果是编辑）
            if content and content != new_content and content in self.standard_hours_data:
                del self.standard_hours_data[content]

            # 保存新的
            self.standard_hours_data[new_content] = new_minutes
            self.save_config()
            self.refresh_table()

            dialog.destroy()
            messagebox.showinfo("成功", "保存成功")

        ttk.Button(button_frame, text="保存", command=save_data).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT)

        # 设置焦点
        content_entry.focus()

        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)

    def import_from_excel(self):
        """从Excel文件导入标准工时"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )

        if not file_path:
            return

        try:
            self.load_from_excel(file_path)
            self.refresh_table()
            messagebox.showinfo("成功", f"成功从 {os.path.basename(file_path)} 导入标准工时配置")
        except Exception as e:
            messagebox.showerror("错误", f"导入失败: {str(e)}")

    def export_to_excel(self):
        """导出标准工时到Excel文件"""
        file_path = filedialog.asksaveasfilename(
            title="保存Excel文件",
            defaultextension=".xlsx",
            filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
        )

        if not file_path:
            return

        try:
            # 创建DataFrame
            data = []
            for content, minutes in sorted(self.standard_hours_data.items()):
                hours_str = self.minutes_to_hours_str(minutes)
                data.append([content, f"{hours_str}:00"])  # 添加秒数以匹配原格式

            df = pd.DataFrame(data, columns=["作业内容", "标准工时"])
            df.to_excel(file_path, index=False)

            messagebox.showinfo("成功", f"成功导出到 {os.path.basename(file_path)}")
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")

    def reset_to_default(self):
        """重置为默认配置"""
        if messagebox.askyesno("确认重置", "确定要重置为默认的标准工时配置吗？这将覆盖当前所有配置。"):
            self.create_default_config()
            self.refresh_table()
            messagebox.showinfo("成功", "已重置为默认配置")


# 测试代码
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    manager = StandardHoursManager()
    manager.show_manager_window()

    root.mainloop()
