{"layout": {"autotypenumbers": "strict", "colorway": ["#F8766D", "#A3A500", "#00BF7D", "#00B0F6", "#E76BF3"], "font": {"color": "rgb(51,51,51)"}, "hovermode": "closest", "hoverlabel": {"align": "left"}, "paper_bgcolor": "white", "plot_bgcolor": "rgb(237,237,237)", "polar": {"bgcolor": "rgb(237,237,237)", "angularaxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}, "radialaxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}}, "ternary": {"bgcolor": "rgb(237,237,237)", "aaxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}, "baxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}, "caxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside"}}, "coloraxis": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "colorscale": {"sequential": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]], "sequentialminus": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]]}, "xaxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside", "title": {"standoff": 15}, "zerolinecolor": "white", "automargin": true}, "yaxis": {"gridcolor": "white", "linecolor": "white", "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside", "title": {"standoff": 15}, "zerolinecolor": "white", "automargin": true}, "scene": {"xaxis": {"backgroundcolor": "rgb(237,237,237)", "gridcolor": "white", "linecolor": "white", "showbackground": true, "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside", "zerolinecolor": "white", "gridwidth": 2}, "yaxis": {"backgroundcolor": "rgb(237,237,237)", "gridcolor": "white", "linecolor": "white", "showbackground": true, "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside", "zerolinecolor": "white", "gridwidth": 2}, "zaxis": {"backgroundcolor": "rgb(237,237,237)", "gridcolor": "white", "linecolor": "white", "showbackground": true, "showgrid": true, "tickcolor": "rgb(51,51,51)", "ticks": "outside", "zerolinecolor": "white", "gridwidth": 2}}, "shapedefaults": {"fillcolor": "black", "line": {"width": 0}, "opacity": 0.3}, "annotationdefaults": {"arrowhead": 0, "arrowwidth": 1}, "geo": {"bgcolor": "white", "landcolor": "rgb(237,237,237)", "subunitcolor": "white", "showland": true, "showlakes": true, "lakecolor": "white"}}, "data": {"histogram2dcontour": [{"type": "histogram2dcontour", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]]}], "choropleth": [{"type": "choropleth", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}], "histogram2d": [{"type": "histogram2d", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]]}], "heatmap": [{"type": "heatmap", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]]}], "contourcarpet": [{"type": "contourcarpet", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}], "contour": [{"type": "contour", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]]}], "surface": [{"type": "surface", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}, "colorscale": [[0, "rgb(20,44,66)"], [1, "rgb(90,179,244)"]]}], "mesh3d": [{"type": "mesh3d", "colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "parcoords": [{"type": "parcoords", "line": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}}], "scatterpolargl": [{"type": "scatterpolargl", "marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}}], "bar": [{"error_x": {"color": "rgb(51,51,51)"}, "error_y": {"color": "rgb(51,51,51)"}, "marker": {"line": {"color": "rgb(237,237,237)", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "scattergeo": [{"type": "scattergeo", "marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}}], "scatterpolar": [{"type": "scatterpolar", "marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "scattergl": [{"type": "scattergl", "marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}}], "scatter3d": [{"type": "scatter3d", "line": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}, "marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}}], "scattermap": [{"type": "scattermap", "marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}}], "scattermapbox": [{"type": "scattermapbox", "marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}}], "scatterternary": [{"type": "scatterternary", "marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}}], "scattercarpet": [{"type": "scattercarpet", "marker": {"colorbar": {"outlinewidth": 0, "tickcolor": "rgb(237,237,237)", "ticklen": 6, "ticks": "inside"}}}], "carpet": [{"aaxis": {"endlinecolor": "rgb(51,51,51)", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "rgb(51,51,51)"}, "baxis": {"endlinecolor": "rgb(51,51,51)", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "rgb(51,51,51)"}, "type": "carpet"}], "table": [{"cells": {"fill": {"color": "rgb(237,237,237)"}, "line": {"color": "white"}}, "header": {"fill": {"color": "rgb(217,217,217)"}, "line": {"color": "white"}}, "type": "table"}], "barpolar": [{"marker": {"line": {"color": "rgb(237,237,237)", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "pie": [{"automargin": true, "type": "pie"}]}}