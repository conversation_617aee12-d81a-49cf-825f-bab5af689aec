# -*- coding: utf-8 -*-
"""
工作计划排程系统
集成了捷通计划处理和汇总计划处理功能
"""
import pandas as pd
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from openpyxl import load_workbook, Workbook
from openpyxl.styles import Alignment, Border, Side, Font, PatternFill, NamedStyle, Color
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.table import Table, TableStyleInfo
from openpyxl.worksheet.dimensions import RowDimension, ColumnDimension
from openpyxl.chart import (
    <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Scatter<PERSON><PERSON>, 
    Reference, Series, BubbleChart, marker
)
from openpyxl.chart.label import DataLabelList
from openpyxl.chart.marker import Marker
from openpyxl.worksheet.datavalidation import DataValidation
from openpyxl.utils.cell import column_index_from_string
from datetime import datetime, timedelta
import logging
import os
import traceback
import re
import random
import threading
import subprocess

from execl_exchange import create_analysis_charts
from auto_scheduler import auto_fill_work_schedule, generate_smart_recommendations, apply_recommended_schedule, evaluate_existing_schedule
from gantt_window import GanttWindow
from schedule_editor_window import ScheduleEditorWindow
from enhanced_gantt_visualizer import EnhancedGanttVisualizer
from work_heatmap_analyzer import WorkHeatmapAnalyzer

# 导入标准工时管理器
try:
    from standard_hours_manager import StandardHoursManager
except ImportError:
    StandardHoursManager = None
    logging.warning("无法导入标准工时管理器，将使用默认逻辑")

# 配置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('processing.log'), logging.StreamHandler()]
)

# 全局变量 - Apple风格配色
VERSION = "1.0.5"
# Apple风格配色方案
THEME_COLOR = "#007AFF"        # Apple蓝色
BG_COLOR = "#F2F2F7"          # Apple浅灰背景
BUTTON_COLOR = "#007AFF"       # Apple蓝色按钮
BUTTON_HOVER = "#0056CC"       # 按钮悬停色
SECONDARY_COLOR = "#8E8E93"    # Apple次要文字色
TEXT_COLOR = "#1C1C1E"         # Apple主文字色
HEADING_COLOR = "#FFFFFF"      # 白色标题
CARD_BG = "#FFFFFF"            # 卡片背景
BORDER_COLOR = "#C6C6C8"       # 边框色
SUCCESS_COLOR = "#34C759"      # 成功绿色
WARNING_COLOR = "#FF9500"      # 警告橙色
ERROR_COLOR = "#FF3B30"        # 错误红色

# ================ 工具函数 ================

def print_cell_values(ws, max_rows=20, max_cols=30):
    """打印工作表前几行的单元格值，帮助调试"""
    print("\n----- Excel内容预览（前几行）-----")
    for r in range(1, min(max_rows, ws.max_row + 1)):
        row_values = []
        for c in range(1, min(max_cols, ws.max_column + 1)):
            value = ws.cell(row=r, column=c).value
            row_values.append(str(value)[:20] if value else "")
        print(f"行 {r}: {' | '.join(row_values)}")
    print("---------------------------------\n")


def analyze_merged_cells(ws):
    """分析合并单元格，创建映射表"""
    merged_map = {}

    if hasattr(ws, 'merged_cells'):
        for merged_range in ws.merged_cells.ranges:
            min_row, min_col, max_row, max_col = merged_range.min_row, merged_range.min_col, merged_range.max_row, merged_range.max_col

            # 对于每个在合并范围内的单元格，记录它的主单元格位置
            for r in range(min_row, max_row + 1):
                for c in range(min_col, max_col + 1):
                    merged_map[(r, c)] = (min_row, min_col)

    return merged_map


def get_cell_value(ws, row, col, merged_map=None):
    """获取单元格值，处理合并单元格的情况"""
    if merged_map and (row, col) in merged_map:
        # 如果是合并单元格的一部分，获取主单元格的值
        main_row, main_col = merged_map[(row, col)]
        return ws.cell(row=main_row, column=main_col).value

    return ws.cell(row=row, column=col).value


def normalize_text(text):
    """规范化文本，去除多余空格并转换为小写"""
    if not text:
        return ""

    # 转为字符串，去除多余空格，转为小写
    return re.sub(r'\s+', '', str(text).strip().lower())


def parse_time_string(time_str):
    """解析时间字符串，支持多种格式"""
    if not time_str or not isinstance(time_str, str):
        return None

    # 尝试解析 HH:MM:SS 格式
    if time_str.count(':') == 2:
        try:
            hour, minute, second = map(int, time_str.split(':'))
            return datetime(1900, 1, 1, hour, minute, second)
        except:
            pass

    # 尝试解析 HH:MM 格式
    if time_str.count(':') == 1:
        try:
            hour, minute = map(int, time_str.split(':'))
            return datetime(1900, 1, 1, hour, minute)
        except:
            pass

    return None


def format_date_no_leading_zeros(date_obj):
    """跨平台的日期格式化，去除前导零"""
    if not date_obj:
        return ""

    # 获取月和日
    month = date_obj.month
    day = date_obj.day

    # 直接使用数字拼接
    return f"{month}/{day}"


def convert_excel_date(excel_date):
    """将Excel日期数值转换为m/d格式"""
    if not excel_date:
        return ""

    try:
        if isinstance(excel_date, (int, float)):
            # Excel日期从1900年1月1日开始，但有个特殊的bug需要调整
            date_obj = datetime(1899, 12, 30) + timedelta(days=int(excel_date))
            return format_date_no_leading_zeros(date_obj)
        elif isinstance(excel_date, datetime):
            return format_date_no_leading_zeros(date_obj)
        elif isinstance(excel_date, str):
            # 尝试将字符串转换为日期
            try:
                date_obj = pd.to_datetime(excel_date)
                return format_date_no_leading_zeros(date_obj)
            except:
                # 如果是正常的Excel序列号形式，如"45754"
                try:
                    if excel_date.isdigit():
                        date_obj = datetime(1899, 12, 30) + timedelta(days=int(excel_date))
                        return format_date_no_leading_zeros(date_obj)
                except:
                    pass
    except Exception as e:
        logging.error(f"日期转换错误: {str(e)}")

    # 如果无法转换，返回原值的字符串形式
    return str(excel_date)


def format_duration(duration_value):
    """将作业时长转换为统一的H:MM格式
    
    支持的输入格式：
    - 字符串形式的时间，如"1:30"
    - 数值形式的小时，如0.5, 1.0
    - 普通字符串，如"0.5", "1"
    
    输出：
    - 格式化为"H:MM"的字符串，如"0:30", "1:00"
    """
    if not duration_value:
        return "0:00"
    
    # 如果是字符串形式的时间，如"1:30"
    if isinstance(duration_value, str) and ":" in duration_value:
        try:
            # 处理可能的异常格式
            parts = duration_value.strip().split(":")
            if len(parts) == 2:
                hours = int(parts[0])
                minutes = int(parts[1])
                return f"{hours}:{minutes:02d}"
            return duration_value  # 如果不能解析，保持原样
        except:
            pass
    
    # 尝试将值转换为数值
    try:
        # 对于数值或字符串形式的数字，如0.5, "1"
        hours_float = float(str(duration_value).strip())
        hours = int(hours_float)
        minutes = int((hours_float - hours) * 60)
        return f"{hours}:{minutes:02d}"
    except:
        # 如果转换失败，返回原始值的字符串形式
        return str(duration_value)


def get_operation_duration(content):
    """根据作业内容确定作业时长
    
    规则：
    1. 当作业内容是JH027-SC待检品或JH027-SD待检品时，作业时长为1小时
    2. 当作业内容是JH027-SD周转箱或JH027-SC周转箱时，作业时长为0.5小时
    """
    if not content:
        return 0.5  # 默认值
    
    content = str(content).strip().lower()
    if "待检品" in content:
        return 1.0
    elif "周转箱" in content:
        return 0.5
    return 0.5  # 默认值


def parse_date_for_sorting(date_str):
    """将m/d格式的日期字符串转换为可排序的元组
    
    例如：
    "4/7" -> (4, 7)
    "12/31" -> (12, 31)
    """
    if not date_str:
        return (0, 0)
    
    try:
        month, day = map(int, date_str.split('/'))
        return (month, day)
    except:
        return (0, 0)


def parse_time_for_sorting(time_str):
    """将HH:MM格式的时间字符串转换为可排序的元组

    例如：
    "08:30" -> (8, 30)
    "14:45" -> (14, 45)
    """
    if not time_str:
        return (0, 0)

    try:
        hour, minute = map(int, time_str.split(':'))
        return (hour, minute)
    except:
        return (0, 0)


def apply_time_adjustment_rules(start_dt, end_dt, duration_hours):
    """应用捷通计划的时间调整规则

    规则1：所有起始时间都需要安排在8:30之后进行
    规则2：避开午休时间11:30-12:30

    参数：
    - start_dt: 起始时间 (datetime)
    - end_dt: 截止时间 (datetime)
    - duration_hours: 作业时长（小时）

    返回：
    - 调整后的 (start_dt, end_dt)
    """
    # 定义关键时间点
    work_start_time = start_dt.replace(hour=8, minute=30, second=0, microsecond=0)  # 8:30
    lunch_start_time = start_dt.replace(hour=11, minute=30, second=0, microsecond=0)  # 11:30
    lunch_end_time = start_dt.replace(hour=12, minute=30, second=0, microsecond=0)  # 12:30

    # 规则1：起始时间必须在8:30之后
    if start_dt < work_start_time:
        logging.info(f"应用规则1：起始时间从 {start_dt.strftime('%H:%M')} 调整到 8:30")
        start_dt = work_start_time
        end_dt = start_dt + timedelta(hours=duration_hours)

    # 规则2：避开午休时间11:30-12:30
    if lunch_start_time <= start_dt <= lunch_end_time:
        # 情况A：起始时间在午休期间，调整到午休结束后
        logging.info(f"应用规则2A：起始时间从 {start_dt.strftime('%H:%M')} 调整到 12:30（避开午休）")
        start_dt = lunch_end_time
        end_dt = start_dt + timedelta(hours=duration_hours)
    elif start_dt < lunch_start_time and end_dt > lunch_start_time:
        # 情况B：起始时间在午休前，但截止时间跨越午休开始时间
        logging.info(f"应用规则2B：截止时间从 {end_dt.strftime('%H:%M')} 延后1小时（跨越午休）")
        end_dt = end_dt + timedelta(hours=1)

    return start_dt, end_dt

# ================ 数据提取与处理 ================

def should_include_record(op_type, content, direction, building=None):
    """判断是否应处理该条数据

    放宽筛选条件，处理更多类型的数据：
    1. 方向为"捷通"
    2. 作业类型为"入库"且作业内容为特定值
    3. 作业内容包含常见的作业关键词（如JH027、JH011等）
    4. 有明确的作业内容且不为空

    排除指定的作业内容：
    - JH011-SH空箱
    - JH011-SH完成品
    - JH027-SB电池
    - JH027-SB周转箱
    - JH027-SA周转箱
    - JH027-SA电池
    - JT026-24电池
    - JT026-24周转箱
    - UF009完成品
    """
    # 规范化字符串处理，去除多余空格，转小写
    op_type_norm = normalize_text(op_type)
    content_norm = normalize_text(content)
    direction_norm = normalize_text(direction)

    # 调试信息
    building_str = f"({building}栋)" if building else ""
    print(f"检查记录{building_str}: 作业类型='{op_type}', 方向='{direction}', 内容='{content}'")

    # 首先检查是否为需要排除的作业内容
    excluded_contents = [
        "jh011-sh空箱",
        "jh011-sh完成品",
        "jh027-sb电池",
        "jh027-sb周转箱",
        "jh027-sa周转箱",
        "jh027-sa电池",
        "jt026-24电池",
        "jt026-24周转箱",
        "uf009完成品"
    ]

    for excluded in excluded_contents:
        if excluded in content_norm:
            return False, f"排除指定作业内容: {excluded}"

    # 条件1：方向为"捷通"
    if "捷通" in direction_norm:
        return True, "方向为捷通"

    # 条件2：作业类型为"入库"且作业内容为特定值
    if "入库" in op_type_norm:
        target_contents = ["jh027-sd周转箱", "jh027-sc周转箱"]
        for target in target_contents:
            if target in content_norm:
                return True, f"入库类型且内容包含{target}"

    # 条件3：作业内容包含常见的作业关键词
    if content_norm:
        common_keywords = ["jh027", "jh011", "待检品", "完成品", "周转箱", "sc", "sd", "sh"]
        for keyword in common_keywords:
            if keyword in content_norm:
                return True, f"作业内容包含关键词'{keyword}'"

    # 条件4：有明确的作业内容且不为空（兜底条件）
    if content and str(content).strip() and str(content).strip() not in ["", "未指定", "无"]:
        return True, "有明确的作业内容"

    # 不满足任何条件
    return False, "不满足任何筛选条件"


def generate_output_filename(input_file, transformed_data):
    """根据输入文件和数据生成输出文件名

    格式：作业计划表ABC栋+捷通20250609~0615_自动排程.xlsx
    """
    try:
        # 提取日期范围
        dates = []
        buildings = set()

        for row in transformed_data:
            date_str = row.get('日期', '')
            building = row.get('栋别', '')

            if date_str and date_str != '':
                # 解析日期格式 m/d
                try:
                    if '/' in date_str:
                        month, day = map(int, date_str.split('/'))
                        # 假设是当前年份，如果月份小于当前月份则是下一年
                        current_year = datetime.now().year
                        current_month = datetime.now().month
                        year = current_year if month >= current_month else current_year + 1
                        date_obj = datetime(year, month, day)
                        dates.append(date_obj)
                except:
                    pass

            if building and building != '':
                buildings.add(building)

        # 生成栋别部分
        building_list = sorted([b for b in buildings if b != '捷通'])  # 先排序ABC栋
        if '捷通' in buildings:
            building_list.append('捷通')

        if building_list:
            if '捷通' in building_list:
                # 如果包含捷通，格式为 "ABC栋+捷通"
                abc_buildings = [b for b in building_list if b != '捷通']
                if abc_buildings:
                    building_part = ''.join(abc_buildings) + '栋+捷通'
                else:
                    building_part = '捷通'
            else:
                # 只有ABC栋
                building_part = ''.join(building_list) + '栋'
        else:
            building_part = 'ABC栋'

        # 生成日期范围部分
        if dates:
            dates.sort()
            start_date = dates[0]
            end_date = dates[-1]

            # 格式化为 YYYYMMDD
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')

            if start_str == end_str:
                date_part = start_str
            else:
                date_part = f"{start_str}~{end_str}"
        else:
            # 如果没有有效日期，使用当前日期
            today = datetime.now()
            date_part = today.strftime('%Y%m%d')

        # 组合文件名
        filename = f"作业计划表{building_part}{date_part}_自动排程.xlsx"

        return filename

    except Exception as e:
        logging.warning(f"生成文件名时出错，使用默认名称: {str(e)}")
        return "汇总计划表.xlsx"


def process_operations_fixed(input_file=None, output_file=None, with_ui=True):
    """处理Excel文件的主函数-修复版"""
    try:
        # 显示当前工作目录
        current_dir = os.getcwd()
        print(f"当前工作目录: {current_dir}")

        # 获取输入文件名
        if input_file is None:
            if with_ui:
                # 使用文件对话框选择文件
                root = tk.Tk()
                root.withdraw()
                input_file = filedialog.askopenfilename(
                    title="选择Excel文件",
                    filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
                )
                if not input_file:
                    print("未选择文件，操作取消")
                    return None
            else:
                # 命令行输入文件名
                input_file = input("请输入Excel文件名(包括扩展名): ")

        # 验证文件是否存在
        if not os.path.exists(input_file):
            error_msg = f"错误：文件 '{input_file}' 不存在，请检查文件名和路径。"
            print(error_msg)
            print(f"当前目录的文件列表: {os.listdir('.')}")
            if with_ui:
                messagebox.showerror("错误", error_msg)
            return None

        logging.info(f"开始处理文件: {input_file}")
        print(f"开始处理文件: {input_file}")

        # 加载工作簿
        wb = load_workbook(filename=input_file, data_only=True, read_only=False)
        ws = wb.active

        # 打印表格内容预览，帮助调试
        print_cell_values(ws, max_rows=30, max_cols=30)

        # 分析合并单元格
        merged_map = analyze_merged_cells(ws)
        print(f"分析到 {len(merged_map)} 个合并单元格位置")

        # 设置各个栋别对应的列范围
        # A栋数据：C列到J列 (列索引 3-10)
        # B栋数据：K列到S列 (列索引 11-19)
        # C栋数据：U列到AB列 (列索引 21-28)
        # 捷通数据：AC列到AJ列 (列索引 29-36)
        
        a_columns = {
            'start_time': 2,  # C列，起始时间
            'end_time': 3,    # D列，截止时间
            'op_time': 4,     # E列，作业时长
            'op_type': 5,     # F列，作业类型
            'direction': 6,   # G列，方向
            'content': 7,     # H列，作业内容
            'quantity': 8,    # I列，数量
            'remark': 9       # J列，备注
        }

        b_columns = {
            # 跳过K列（索引10）
            'start_time': 11,    # L列，起始时间
            'end_time': 12,      # M列，截止时间
            'op_time': 13,       # N列，作业时长
            'op_type': 14,       # O列，作业类型
            'direction': 15,     # P列，方向
            'content': 16,       # Q列，作业内容
            'quantity': 17,      # R列，数量
            'remark': 18         # S列，备注
        }
        
        c_columns = {
            'start_time': 20,  # U列，起始时间
            'end_time': 21,    # V列，截止时间
            'op_time': 22,     # W列，作业时长
            'op_type': 23,     # X列，作业类型
            'direction': 24,   # Y列，方向
            'content': 25,     # Z列，作业内容
            'quantity': 26,    # AA列，数量
            'remark': 27       # AB列，备注
        }
        
        jt_columns = {
            'start_time': 28,  # AC列，起始时间
            'end_time': 29,    # AD列，截止时间
            'op_time': 30,     # AE列，作业时长
            'op_type': 31,     # AF列，作业类型
            'direction': 32,   # AG列，方向
            'content': 33,     # AH列，作业内容
            'quantity': 34,    # AI列，数量
            'remark': 35       # AJ列，备注
        }

        # 公共列（所有栋别共用）
        common_columns = {
            'date': 0,      # A列，日期
            'weekday': 1,   # B列，星期
        }

        print("\n各栋别列映射:")
        print("A栋: C-J列 (3-10)")
        print("B栋: K-S列 (11-19)")
        print("C栋: U-AB列 (21-28)")
        print("捷通: AC-AJ列 (29-36)")

        # 处理数据
        all_rows_data = []
        current_date = None
        current_weekday = None

        # 扫描所有行，提取数据
        # 扩展扫描范围到9999行，确保不遗漏任何数据
        max_scan_row = max(ws.max_row, 9999)
        for row_idx in range(3, max_scan_row + 1):  # 从第3行开始，跳过表头
            try:
                # 处理日期和星期合并单元格
                date_value = get_cell_value(ws, row_idx, common_columns['date'] + 1, merged_map)
                if date_value:
                    current_date = date_value

                weekday_value = get_cell_value(ws, row_idx, common_columns['weekday'] + 1, merged_map)
                if weekday_value:
                    current_weekday = weekday_value

                # 收集A栋数据
                a_start_time = get_cell_value(ws, row_idx, a_columns['start_time'] + 1, merged_map)
                a_end_time = get_cell_value(ws, row_idx, a_columns['end_time'] + 1, merged_map)
                a_op_type = get_cell_value(ws, row_idx, a_columns['op_type'] + 1, merged_map)
                a_direction = get_cell_value(ws, row_idx, a_columns['direction'] + 1, merged_map)
                a_content = get_cell_value(ws, row_idx, a_columns['content'] + 1, merged_map)

                # 只要有一个核心字段有值，就认为是有效行数据
                if any([a_op_type, a_content, a_direction, a_start_time, a_end_time]):
                    row_data = {
                        '栋别': 'A',
                        '日期': current_date,
                        '星期': current_weekday,
                        '起始时间': a_start_time,
                        '截止时间': a_end_time,
                        '作业时间': get_cell_value(ws, row_idx, a_columns['op_time'] + 1, merged_map),
                        '作业类型': a_op_type or "未指定",
                        '方向': a_direction or "未指定",
                        '作业内容': a_content or "未指定",
                        '数量': get_cell_value(ws, row_idx, a_columns['quantity'] + 1, merged_map),
                        '备注': get_cell_value(ws, row_idx, a_columns['remark'] + 1, merged_map),
                        '行号': row_idx
                    }
                    all_rows_data.append(row_data)

                # 收集B栋数据 - 注意：不处理K列（索引10）
                b_start_time = get_cell_value(ws, row_idx, b_columns['start_time'] + 1, merged_map)
                b_end_time = get_cell_value(ws, row_idx, b_columns['end_time'] + 1, merged_map)
                b_op_type = get_cell_value(ws, row_idx, b_columns['op_type'] + 1, merged_map)
                b_direction = get_cell_value(ws, row_idx, b_columns['direction'] + 1, merged_map)
                b_content = get_cell_value(ws, row_idx, b_columns['content'] + 1, merged_map)

                # 只要有一个核心字段有值，就认为是有效行数据
                if any([b_op_type, b_content, b_direction, b_start_time, b_end_time]):
                    row_data = {
                        '栋别': 'B',
                        '日期': current_date,
                        '星期': current_weekday,
                        '起始时间': b_start_time,
                        '截止时间': b_end_time,
                        '作业时间': get_cell_value(ws, row_idx, b_columns['op_time'] + 1, merged_map),
                        '作业类型': b_op_type or "未指定",
                        '方向': b_direction or "未指定",
                        '作业内容': b_content or "未指定",
                        '数量': get_cell_value(ws, row_idx, b_columns['quantity'] + 1, merged_map),
                        '备注': get_cell_value(ws, row_idx, b_columns['remark'] + 1, merged_map),
                        '行号': row_idx
                    }
                    all_rows_data.append(row_data)
                    
                # 收集C栋数据
                c_start_time = get_cell_value(ws, row_idx, c_columns['start_time'] + 1, merged_map)
                c_end_time = get_cell_value(ws, row_idx, c_columns['end_time'] + 1, merged_map)
                c_op_type = get_cell_value(ws, row_idx, c_columns['op_type'] + 1, merged_map)
                c_direction = get_cell_value(ws, row_idx, c_columns['direction'] + 1, merged_map)
                c_content = get_cell_value(ws, row_idx, c_columns['content'] + 1, merged_map)

                # 只要有一个核心字段有值，就认为是有效行数据
                if any([c_op_type, c_content, c_direction, c_start_time, c_end_time]):
                    row_data = {
                        '栋别': 'C',
                        '日期': current_date,
                        '星期': current_weekday,
                        '起始时间': c_start_time,
                        '截止时间': c_end_time,
                        '作业时间': get_cell_value(ws, row_idx, c_columns['op_time'] + 1, merged_map),
                        '作业类型': c_op_type or "未指定",
                        '方向': c_direction or "未指定",
                        '作业内容': c_content or "未指定",
                        '数量': get_cell_value(ws, row_idx, c_columns['quantity'] + 1, merged_map),
                        '备注': get_cell_value(ws, row_idx, c_columns['remark'] + 1, merged_map),
                        '行号': row_idx
                    }
                    all_rows_data.append(row_data)
                
                # 收集捷通数据
                jt_start_time = get_cell_value(ws, row_idx, jt_columns['start_time'] + 1, merged_map)
                jt_end_time = get_cell_value(ws, row_idx, jt_columns['end_time'] + 1, merged_map)
                jt_op_type = get_cell_value(ws, row_idx, jt_columns['op_type'] + 1, merged_map)
                jt_direction = get_cell_value(ws, row_idx, jt_columns['direction'] + 1, merged_map)
                jt_content = get_cell_value(ws, row_idx, jt_columns['content'] + 1, merged_map)

                # 只要有一个核心字段有值，就认为是有效行数据
                if any([jt_op_type, jt_content, jt_direction, jt_start_time, jt_end_time]):
                    row_data = {
                        '栋别': '捷通',
                        '日期': current_date,
                        '星期': current_weekday,
                        '起始时间': jt_start_time,
                        '截止时间': jt_end_time,
                        '作业时间': get_cell_value(ws, row_idx, jt_columns['op_time'] + 1, merged_map),
                        '作业类型': jt_op_type or "未指定",
                        '方向': jt_direction or "未指定",
                        '作业内容': jt_content or "未指定",
                        '数量': get_cell_value(ws, row_idx, jt_columns['quantity'] + 1, merged_map),
                        '备注': get_cell_value(ws, row_idx, jt_columns['remark'] + 1, merged_map),
                        '行号': row_idx
                    }
                    all_rows_data.append(row_data)

            except Exception as e:
                logging.error(f"处理第{row_idx}行时出错: {str(e)}")
                logging.error(traceback.format_exc())

        print(f"\n共收集到 {len(all_rows_data)} 条原始记录")

        # 取消筛选，直接处理所有记录
        rows_data = all_rows_data
        print(f"\n总共找到 {len(rows_data)} 条记录待处理")

        # 创建转换后的数据
        transformed_data = []

        for row in rows_data:
            try:
                # 处理日期 - 确保格式为m/d
                formatted_date = convert_excel_date(row['日期'])

                # 标记时间是否为空
                start_time = row['起始时间']
                end_time = row['截止时间']
                time_is_empty = not start_time or not end_time

                # 如果时间为空，创建一条带警告的记录
                if time_is_empty:
                    new_row = {
                        '日期': formatted_date,
                        '栋别': row['栋别'],
                        '起始时间': start_time or "需检查",
                        '截止时间': end_time or "需检查",
                        '作业时长': format_duration(row.get('作业时间', 0.5)),
                        '作业类型': row['作业类型'],
                        '方向': row['方向'],
                        '作业内容': row['作业内容'],
                        '数量': row['数量'],
                        '备注': f"{row['备注'] if row['备注'] else ''} 检查源表格中数据[行{row['行号']}]".strip(),
                        '实际开始时间': '',  # 新增字段
                        '实际结束时间': '',  # 新增字段
                        '实际作业时长': '',  # 新增字段 - 由Excel公式计算
                        '异常原因': ''  # 保持为空
                    }
                    transformed_data.append(new_row)
                    print(f"已转换(时间为空): 栋别:{row['栋别']}, 日期:{formatted_date}, 行号:{row['行号']}")
                    continue

                # 处理时间 - 尝试解析起始时间和结束时间
                try:
                    # 解析起始时间
                    start_time_str = str(start_time).strip() if start_time else None
                    if isinstance(start_time, datetime):
                        start_dt = start_time
                    elif start_time_str:
                        # 尝试解析时间字符串
                        start_dt = parse_time_string(start_time_str)
                        # 如果失败，进一步尝试解析
                        if not start_dt and ':' in start_time_str:
                            parts = start_time_str.split(':')
                            if len(parts) >= 2:
                                try:
                                    hour = int(parts[0])
                                    minute = int(parts[1])
                                    start_dt = datetime(1900, 1, 1, hour, minute)
                                except:
                                    start_dt = None
                    else:
                        start_dt = None

                    # 解析结束时间
                    end_time_str = str(end_time).strip() if end_time else None
                    if isinstance(end_time, datetime):
                        end_dt = end_time
                    elif end_time_str:
                        # 尝试解析时间字符串
                        end_dt = parse_time_string(end_time_str)
                        # 如果失败，进一步尝试解析
                        if not end_dt and ':' in end_time_str:
                            parts = end_time_str.split(':')
                            if len(parts) >= 2:
                                try:
                                    hour = int(parts[0])
                                    minute = int(parts[1])
                                    end_dt = datetime(1900, 1, 1, hour, minute)
                                except:
                                    end_dt = None
                    else:
                        end_dt = None

                    # 如果解析失败，使用原始字符串
                    start_time_formatted = start_time_str if not start_dt else start_dt.strftime('%H:%M')
                    end_time_formatted = end_time_str if not end_dt else end_dt.strftime('%H:%M')
                    
                    # 尝试获取作业时长，如果不能解析则使用原始值或默认值
                    if row.get('作业时间') is not None:
                        try:
                            duration = row['作业时间']
                        except:
                            duration = get_operation_duration(row['作业内容'])
                    else:
                        duration = get_operation_duration(row['作业内容'])

                except Exception as e:
                    logging.warning(f"时间解析错误，使用原始值: {str(e)}")
                    start_time_formatted = str(start_time) if start_time else ""
                    end_time_formatted = str(end_time) if end_time else ""
                    duration = get_operation_duration(row['作业内容'])

                # 创建新行数据 - 保持原始数据，不做转换
                new_row = {
                    '日期': formatted_date,
                    '栋别': row['栋别'],
                    '起始时间': start_time_formatted,
                    '截止时间': end_time_formatted,
                    '作业时长': format_duration(duration),
                    '作业类型': row['作业类型'],
                    '方向': row['方向'],
                    '作业内容': row['作业内容'],
                    '数量': row['数量'],
                    '备注': row['备注'],
                    '实际开始时间': '',  # 新增字段
                    '实际结束时间': '',  # 新增字段
                    '实际作业时长': '',  # 新增字段 - 由Excel公式计算
                    '异常原因': ''       # 新增字段
                }
                transformed_data.append(new_row)
                print(f"已转换: 栋别:{row['栋别']}, 日期:{formatted_date}, 行号:{row['行号']}")
            except Exception as e:
                logging.error(f"转换数据时出错: {str(e)}")
                logging.error(traceback.format_exc())
                print(f"转换数据时出错: {str(e)}")

        # 按日期和栋别排序（从早到晚）
        transformed_data.sort(key=lambda x: (
            parse_date_for_sorting(x['日期']),
            x['栋别']
        ))

        # 创建DataFrame并输出到Excel
        if transformed_data:
            # 如果没有指定输出文件名，生成智能文件名
            if output_file is None:
                default_output = generate_output_filename(input_file, transformed_data)
                if with_ui:
                    # 使用文件对话框选择保存位置
                    root = tk.Tk()
                    root.withdraw()
                    output_file = filedialog.asksaveasfilename(
                        title="另存为",
                        filetypes=[("Excel文件", "*.xlsx")],
                        defaultextension=".xlsx",
                        initialfile=default_output
                    )
                    if not output_file:
                        print("未选择保存位置，操作取消")
                        return None
                else:
                    output_file = default_output

            # 确保所有字段都出现在每条记录中(防止缺失导致的列不对齐)
            all_fields = [
                '日期', '栋别', '起始时间', '截止时间', '作业时长',
                '作业类型', '方向', '作业内容', '数量', '备注',
                '实际开始时间', '实际结束时间', '实际作业时长', '异常原因'
            ]

            # 确保每条记录都包含所有字段
            for record in transformed_data:
                for field in all_fields:
                    if field not in record:
                        record[field] = ''

            # 创建DataFrame并指定列顺序
            df = pd.DataFrame(transformed_data)[all_fields]

            # 保存到Excel
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Sheet1')

                # 获取工作簿并美化表格
                workbook = writer.book
                apply_table_style(workbook, 'Sheet1')

                # 设置打印区域和页面设置
                ws = workbook['Sheet1']
                ws.print_area = f'A1:{get_column_letter(len(all_fields))}{ws.max_row}'
                ws.page_setup.orientation = 'landscape'  # 横向打印
                ws.page_setup.fitToWidth = 1  # 调整为1页宽
                ws.page_setup.fitToHeight = 0  # 高度自适应
                
                # 添加自动计算实际作业时长的公式
                add_actual_duration_formula(ws)
                
                # 创建分析图表sheet
                create_analysis_charts(workbook, 'Sheet1')
                
                # 添加说明信息
                ws = workbook['Sheet1']
                note_row = ws.max_row + 2
                ws.cell(row=note_row, column=1).value = "使用说明：填写'实际开始时间'、'实际结束时间'和'异常原因'字段，然后在DataAnalysis页查看自动更新的数据分析图表。"
                ws.cell(row=note_row, column=1).font = Font(italic=True, color="4472C4")
                ws.merge_cells(start_row=note_row, start_column=1, end_row=note_row, end_column=10)
                
                # 保存更改
                workbook.save(output_file)
            
            success_msg = f"\n处理完成，成功处理 {len(transformed_data)} 条记录，结果已保存到 {output_file}"
            print(success_msg)
            if with_ui:
                messagebox.showinfo("处理完成", success_msg)
            return output_file
        else:
            empty_msg = "\n没有找到符合条件的数据"
            print(empty_msg)
            if with_ui:
                messagebox.showwarning("提示", empty_msg)
            return None

    except Exception as e:
        error_msg = f"处理过程中发生错误：{str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        print(error_msg)
        if with_ui:
            messagebox.showerror("错误", error_msg)
        return None

# ================ 表格美化与样式应用 ================

def apply_table_style(wb, sheet_name):
    """美化表格 - 添加样式、边框、颜色等"""
    ws = wb[sheet_name]

    # 定义边框样式
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # 定义标题样式
    header_font = Font(name='Arial', size=11, bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # 定义偶数行样式
    even_row_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    
    # 定义栋别样式
    a_building_fill = PatternFill(start_color="FFD966", end_color="FFD966", fill_type="solid")
    b_building_fill = PatternFill(start_color="A9D08E", end_color="A9D08E", fill_type="solid")
    c_building_fill = PatternFill(start_color="F4B084", end_color="F4B084", fill_type="solid")
    jt_building_fill = PatternFill(start_color="9BC2E6", end_color="9BC2E6", fill_type="solid")

    # 获取表格的行数和列数
    max_row = ws.max_row
    max_col = ws.max_column

    # 应用样式到所有单元格
    for row in range(1, max_row + 1):
        # 设置行高
        ws.row_dimensions[row].height = 20
        
        for col in range(1, max_col + 1):
            cell = ws.cell(row=row, column=col)
            
            # 添加边框
            cell.border = thin_border
            
            # 应用标题样式（第一行）
            if row == 1:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            else:
                # 设置数据行文本垂直居中
                cell.alignment = Alignment(horizontal='center', vertical='center')
                
                # 应用偶数行背景色
                if row % 2 == 0:
                    cell.fill = even_row_fill
                
                # 应用栋别颜色 - 第2列是栋别列
                if col == 2:  # 栋别列
                    building_value = str(cell.value).strip() if cell.value else ""
                    if building_value == 'A':
                        cell.fill = a_building_fill
                    elif building_value == 'B':
                        cell.fill = b_building_fill
                    elif building_value == 'C':
                        cell.fill = c_building_fill
                    elif building_value == '捷通':
                        cell.fill = jt_building_fill
                        
                # 为日期列设置左对齐
                if col == 1:  # 日期列
                    cell.alignment = Alignment(horizontal='left', vertical='center')

    # 设置列宽
    column_widths = {
        1: 10,   # 日期
        2: 8,    # 栋别
        3: 10,   # 起始时间
        4: 10,   # 截止时间
        5: 8,    # 作业时长
        6: 10,   # 作业类型
        7: 10,   # 方向
        8: 30,   # 作业内容
        9: 8,    # 数量
        10: 20,  # 备注
        11: 10,  # 实际开始时间
        12: 10,  # 实际结束时间
        13: 10,  # 实际作业时长
        14: 20,  # 异常原因
    }
    
    # 设置精确的列宽并自动调整
    for col, width in column_widths.items():
        if col <= max_col:
            column_letter = get_column_letter(col)
            ws.column_dimensions[column_letter].width = width
    
    # 自动调整未明确设置宽度的列
    for col in range(len(column_widths) + 1, max_col + 1):
        column_letter = get_column_letter(col)
        ws.column_dimensions[column_letter].auto_size = True
        # 设置一个最小宽度
        if ws.column_dimensions[column_letter].width < 10:
            ws.column_dimensions[column_letter].width = 10
    
    # 冻结首行和首列
    ws.freeze_panes = 'B2'
    
    # 启用自动筛选
    if max_row > 1:
        ws.auto_filter.ref = f"A1:{get_column_letter(max_col)}{max_row}"

    print(f"表格样式应用完成 - 共 {max_row} 行, {max_col} 列")


def add_actual_duration_formula(ws):
    """添加计算实际作业时长的公式
    
    在用户填写实际开始时间和实际结束时间后，自动计算实际作业时长
    """
    # 获取实际开始时间、实际结束时间和实际作业时长的列索引
    actual_start_col = 11  # K列
    actual_end_col = 12    # L列
    actual_duration_col = 13  # M列
    
    # 从第2行开始（跳过标题行）
    for row in range(2, ws.max_row + 1):
        start_cell = f"{get_column_letter(actual_start_col)}{row}"
        end_cell = f"{get_column_letter(actual_end_col)}{row}"
        duration_cell = f"{get_column_letter(actual_duration_col)}{row}"
        
        # 计算逻辑: 如果开始和结束时间都有，则计算时间差并格式化为"H:MM"
        formula = f'=IF(AND({start_cell}<>"",{end_cell}<>""), TEXT(({end_cell}-{start_cell}),"[h]:mm"), "")'
        ws[duration_cell] = formula
    
    print(f"已添加实际作业时长计算公式到 {ws.max_row - 1} 行")


def generate_mock_actual_data(ws):
    """
    生成模拟的实际开始时间、结束时间和异常原因数据
    用于测试分析图表功能
    """
    # 获取列索引
    plan_start_col = 3  # 计划起始时间列
    plan_end_col = 4    # 计划截止时间列
    actual_start_col = 11  # 实际开始时间列
    actual_end_col = 12    # 实际结束时间列
    actual_duration_col = 13  # 实际作业时长列 - 由公式计算，无需在此设置
    abnormal_reason_col = 14  # 异常原因列
    
    # 可能的异常原因列表
    abnormal_reasons = [
        "车辆晚到", "装卸设备故障", "货物未准备好", "人员不足", 
        "天气原因", "交通拥堵", "系统故障", "前序工作延误",
        "", "", "", ""  # 添加空字符串，使部分记录没有异常原因
    ]
    
    print("正在生成模拟的实际时间数据...")
    
    # 从第2行开始（跳过标题行）
    modified_rows = 0
    for row in range(2, ws.max_row + 1):
        # 获取计划时间
        plan_start = ws.cell(row=row, column=plan_start_col).value
        plan_end = ws.cell(row=row, column=plan_end_col).value
        
        # 跳过没有计划时间的行
        if not plan_start or not isinstance(plan_start, str):
            continue
            
        # 解析计划时间
        try:
            # 解析时间字符串 (例如 "08:30")
            if ":" in plan_start:
                plan_h, plan_m = map(int, plan_start.split(":"))
                plan_start_dt = datetime(1900, 1, 1, plan_h, plan_m)
                
                # 随机偏差：-30分钟到+60分钟
                time_diff = random.randint(-30, 60)
                
                # 创建实际开始时间
                actual_start_dt = plan_start_dt + timedelta(minutes=time_diff)
                actual_start_time = actual_start_dt.strftime("%H:%M")
                
                # 创建实际结束时间（在实际开始时间基础上随机增加30-120分钟）
                actual_duration_minutes = random.randint(30, 120)
                actual_end_dt = actual_start_dt + timedelta(minutes=actual_duration_minutes)
                actual_end_time = actual_end_dt.strftime("%H:%M")
                
                # 设置实际开始和结束时间
                ws.cell(row=row, column=actual_start_col).value = actual_start_time
                ws.cell(row=row, column=actual_end_col).value = actual_end_time
                
                # 实际作业时长由Excel公式计算，不在此处设置
                
                # 根据时间差设置异常原因
                if time_diff < -5:
                    # 提前到达
                    ws.cell(row=row, column=abnormal_reason_col).value = "提前到达"
                elif time_diff > 15:
                    # 晚到
                    ws.cell(row=row, column=abnormal_reason_col).value = random.choice(abnormal_reasons)
                
                modified_rows += 1
        except:
            # 如果解析失败，跳过此行
            continue
    
    print(f"已生成 {modified_rows} 行模拟数据")
    return modified_rows

# 现代化Apple风格按钮类
class AppleButton(tk.Button):
    def __init__(self, parent, **kwargs):
        # 提取自定义参数
        self.hover_color = kwargs.pop('hover_color', BUTTON_HOVER)
        self.normal_color = kwargs.pop('bg', BUTTON_COLOR)
        self.text_color = kwargs.pop('fg', HEADING_COLOR)

        # 设置现代化默认样式
        default_style = {
            'relief': tk.FLAT,
            'borderwidth': 0,
            'cursor': 'hand2',
            'font': ("SF Pro Display", 13, "bold"),
            'bg': self.normal_color,
            'fg': self.text_color,
            'activebackground': self.hover_color,
            'activeforeground': self.text_color,
            'padx': 20,
            'pady': 12,
            'compound': tk.LEFT,  # 图标和文字的排列方式
            'anchor': tk.CENTER   # 文字居中对齐
        }

        # 合并用户提供的参数
        default_style.update(kwargs)

        super().__init__(parent, **default_style)

        # 绑定悬停事件和点击效果
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)
        self.bind("<Button-1>", self.on_click)
        self.bind("<ButtonRelease-1>", self.on_release)

    def on_enter(self, event):
        """鼠标悬停时的效果 - 添加阴影效果"""
        self.config(bg=self.hover_color)
        # 可以在这里添加更多视觉效果，如阴影

    def on_leave(self, event):
        """鼠标离开时的效果"""
        self.config(bg=self.normal_color)

    def on_click(self, event):
        """鼠标点击时的效果 - 轻微缩放效果"""
        # 模拟按钮按下的视觉反馈
        self.config(relief=tk.SUNKEN)

    def on_release(self, event):
        """鼠标释放时的效果"""
        self.config(relief=tk.FLAT)

# 主应用类
class WorkingScheduleApp:
    def __init__(self, root):
        self.root = root
        self.root.title(f"🏭 工作计划排程系统 v{VERSION}")
        self.root.geometry("1000x700")
        self.root.configure(bg=BG_COLOR)
        self.root.minsize(1000, 700)

        # 设置窗口居中
        self.center_window()

        # 创建菜单栏
        self.create_menu()

        # 创建主框架 - Apple风格的卡片布局
        self.main_frame = tk.Frame(
            self.root,
            bg=BG_COLOR,
            relief=tk.FLAT,
            borderwidth=0
        )
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # 初始化甘特图窗口
        self.gantt_window = None

        # 初始化编辑器窗口
        self.editor_window = None

        # 初始化标准工时管理器
        if StandardHoursManager:
            self.standard_hours_manager = StandardHoursManager(parent=self.root)
        else:
            self.standard_hours_manager = None

        # 显示欢迎页面
        self.show_welcome_page()

    def center_window(self):
        """将窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="🚪 退出", command=self.root.quit)
        menubar.add_cascade(label="📁 文件", menu=file_menu)
        
        # 操作菜单
        operations_menu = tk.Menu(menubar, tearoff=0)
        operations_menu.add_command(label="🚚 捷通计划处理", command=self.show_jt_processing)
        operations_menu.add_command(label="📋 汇总计划处理", command=self.show_summary_processing)
        operations_menu.add_command(label="📈 数据分析更新", command=self.show_analytics_update)
        operations_menu.add_separator()
        operations_menu.add_command(label="⏰ 自动时间排程", command=self.show_auto_schedule)
        operations_menu.add_command(label="🎲 生成模拟数据", command=self.generate_mock_data)
        operations_menu.add_separator()
        operations_menu.add_command(label="✏️ 排程编辑器", command=self.show_schedule_editor)
        operations_menu.add_command(label="⏱️ 标准工时管理", command=self.show_standard_hours_manager)
        menubar.add_cascade(label="🔧 操作", menu=operations_menu)

        # 可视化菜单
        visualization_menu = tk.Menu(menubar, tearoff=0)
        visualization_menu.add_command(label="📊 甘特图可视化", command=self.show_gantt_chart)
        menubar.add_cascade(label="📊 可视化", menu=visualization_menu)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="📖 使用说明", command=self.show_help)
        help_menu.add_command(label="ℹ️ 关于", command=self.show_about)
        menubar.add_cascade(label="❓ 帮助", menu=help_menu)
        
        self.root.config(menu=menubar)
    
    def show_welcome_page(self):
        """显示欢迎页面"""
        # 清除主框架
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # 创建欢迎页面
        welcome_frame = tk.Frame(self.main_frame, bg=BG_COLOR)
        welcome_frame.pack(fill=tk.BOTH, expand=True)
        
        # Apple风格标题
        title_label = tk.Label(
            welcome_frame,
            text="🏭 工作计划排程系统",
            font=("SF Pro Display", 32, "bold"),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        title_label.pack(pady=(60, 20))

        # 版本信息
        version_label = tk.Label(
            welcome_frame,
            text=f"版本 {VERSION}",
            font=("SF Pro Display", 16, "normal"),
            fg=SECONDARY_COLOR,
            bg=BG_COLOR
        )
        version_label.pack(pady=(0, 60))
        
        # Apple风格功能按钮框架
        button_frame = tk.Frame(
            welcome_frame,
            bg=BG_COLOR,
            relief=tk.FLAT,
            borderwidth=0
        )
        button_frame.pack(pady=30)
        
        # 统一的Apple风格功能按钮样式
        base_button_style = {
            "width": 18,
            "height": 2,
            "font": ("SF Pro Display", 13, "bold"),
            "relief": tk.FLAT,
            "borderwidth": 0,
            "cursor": "hand2"
        }

        # 定义按钮配色方案 - 8种独特颜色，确保每个功能模块都有唯一的颜色
        button_colors = {
            "auto_schedule": {"bg": "#1E88E5", "hover": "#1565C0", "text": "#FFFFFF"},    # 自动时间排程 - 深蓝色
            "editor": {"bg": "#FF5722", "hover": "#E64A19", "text": "#FFFFFF"},          # 排程编辑器 - 橙红色
            "jt_process": {"bg": "#7B1FA2", "hover": "#6A1B9A", "text": "#FFFFFF"},      # 捷通计划处理 - 深紫色
            "summary": {"bg": "#3F51B5", "hover": "#303F9F", "text": "#FFFFFF"},         # 汇总计划处理 - 靛蓝色
            "gantt": {"bg": "#4CAF50", "hover": "#388E3C", "text": "#FFFFFF"},           # 甘特图可视化 - 翠绿色
            "heatmap": {"bg": "#00BCD4", "hover": "#0097A7", "text": "#FFFFFF"},         # 作业热力图分析 - 青色
            "analytics": {"bg": "#FFC107", "hover": "#FFA000", "text": "#000000"},       # 数据分析更新 - 琥珀色（黑字）
            "mock_data": {"bg": "#607D8B", "hover": "#455A64", "text": "#FFFFFF"},       # 生成模拟数据 - 蓝灰色
            "help": {"bg": "#795548", "hover": "#5D4037", "text": "#FFFFFF"}             # 使用说明 - 棕色
        }

        # 按照用户要求的顺序创建按钮：
        # 自动时间排程，排程编辑器，捷通计划处理，汇总计划处理，甘特图可视化，数据分析更新，生成模拟数据，使用说明

        # 第一行：自动时间排程 + 排程编辑器
        auto_schedule_btn = AppleButton(
            button_frame,
            text="⏰ 自动时间排程",
            command=self.show_auto_schedule,
            bg=button_colors["auto_schedule"]["bg"],
            hover_color=button_colors["auto_schedule"]["hover"],
            fg=button_colors["auto_schedule"]["text"],
            **base_button_style
        )
        auto_schedule_btn.grid(row=0, column=0, padx=20, pady=12, sticky="ew")

        editor_btn = AppleButton(
            button_frame,
            text="✏️ 排程编辑器",
            command=self.show_schedule_editor,
            bg=button_colors["editor"]["bg"],
            hover_color=button_colors["editor"]["hover"],
            fg=button_colors["editor"]["text"],
            **base_button_style
        )
        editor_btn.grid(row=0, column=1, padx=20, pady=12, sticky="ew")

        # 第二行：捷通计划处理 + 汇总计划处理
        jt_btn = AppleButton(
            button_frame,
            text="🚚 捷通计划处理",
            command=self.show_jt_processing,
            bg=button_colors["jt_process"]["bg"],
            hover_color=button_colors["jt_process"]["hover"],
            fg=button_colors["jt_process"]["text"],
            **base_button_style
        )
        jt_btn.grid(row=1, column=0, padx=20, pady=12, sticky="ew")

        summary_btn = AppleButton(
            button_frame,
            text="📋 汇总计划处理",
            command=self.show_summary_processing,
            bg=button_colors["summary"]["bg"],
            hover_color=button_colors["summary"]["hover"],
            fg=button_colors["summary"]["text"],
            **base_button_style
        )
        summary_btn.grid(row=1, column=1, padx=20, pady=12, sticky="ew")

        # 第三行：甘特图可视化 + 作业热力图分析
        gantt_btn = AppleButton(
            button_frame,
            text="📊 甘特图可视化",
            command=self.show_gantt_chart,
            bg=button_colors["gantt"]["bg"],
            hover_color=button_colors["gantt"]["hover"],
            fg=button_colors["gantt"]["text"],
            **base_button_style
        )
        gantt_btn.grid(row=2, column=0, padx=20, pady=12, sticky="ew")

        heatmap_btn = AppleButton(
            button_frame,
            text="🔥 作业热力图分析",
            command=self.show_heatmap_analysis,
            bg=button_colors["heatmap"]["bg"],
            hover_color=button_colors["heatmap"]["hover"],
            fg=button_colors["heatmap"]["text"],
            **base_button_style
        )
        heatmap_btn.grid(row=2, column=1, padx=20, pady=12, sticky="ew")

        # 第四行：数据分析更新 + 生成模拟数据
        analytics_btn = AppleButton(
            button_frame,
            text="📈 数据分析更新",
            command=self.show_analytics_update,
            bg=button_colors["analytics"]["bg"],
            hover_color=button_colors["analytics"]["hover"],
            fg=button_colors["analytics"]["text"],
            **base_button_style
        )
        analytics_btn.grid(row=3, column=0, padx=20, pady=12, sticky="ew")

        mock_btn = AppleButton(
            button_frame,
            text="🎲 生成模拟数据",
            command=self.generate_mock_data,
            bg=button_colors["mock_data"]["bg"],
            hover_color=button_colors["mock_data"]["hover"],
            fg=button_colors["mock_data"]["text"],
            **base_button_style
        )
        mock_btn.grid(row=3, column=1, padx=20, pady=12, sticky="ew")

        # 第五行：使用说明（居中）
        help_btn = AppleButton(
            button_frame,
            text="📖 使用说明",
            command=self.show_help,
            bg=button_colors["help"]["bg"],
            hover_color=button_colors["help"]["hover"],
            fg=button_colors["help"]["text"],
            **base_button_style
        )
        help_btn.grid(row=4, column=0, columnspan=2, padx=20, pady=12, sticky="ew")

        # 配置网格权重，使按钮能够均匀分布
        button_frame.grid_columnconfigure(0, weight=1)
        button_frame.grid_columnconfigure(1, weight=1)
        
        # Apple风格页脚
        footer_frame = tk.Frame(welcome_frame, bg=BG_COLOR)
        footer_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=30)

        footer_text = tk.Label(
            footer_frame,
            text="© 2025 智能作业计划排程系统 ✨",
            font=("SF Pro Display", 12, "normal"),
            fg=SECONDARY_COLOR,
            bg=BG_COLOR
        )
        footer_text.pack()

    def show_gantt_chart(self):
        """显示甘特图可视化选择窗口"""
        try:
            # 创建选择窗口
            self.show_gantt_selection_window()
        except Exception as e:
            logging.error(f"显示甘特图窗口失败: {str(e)}")
            messagebox.showerror("错误", f"显示甘特图窗口失败: {str(e)}")

    def show_gantt_selection_window(self):
        """显示甘特图类型选择窗口"""
        selection_window = tk.Toplevel(self.root)
        selection_window.title("甘特图可视化选择")
        selection_window.geometry("600x400")
        selection_window.configure(bg=BG_COLOR)
        selection_window.resizable(False, False)

        # 设置窗口图标
        try:
            if os.path.exists("favicon.ico"):
                selection_window.iconbitmap("favicon.ico")
        except:
            pass

        # 居中显示
        selection_window.transient(self.root)
        selection_window.grab_set()

        # 主框架
        main_frame = tk.Frame(selection_window, bg=BG_COLOR)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # 标题
        title_label = tk.Label(
            main_frame,
            text="甘特图可视化方案选择",
            font=("Microsoft YaHei", 18, "bold"),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        title_label.pack(pady=(0, 30))

        # 说明文本
        desc_label = tk.Label(
            main_frame,
            text="请选择您需要的甘特图可视化方案：",
            font=("Microsoft YaHei", 12),
            fg=SECONDARY_COLOR,
            bg=BG_COLOR
        )
        desc_label.pack(pady=(0, 20))

        # 按钮框架
        button_frame = tk.Frame(main_frame, bg=BG_COLOR)
        button_frame.pack(fill=tk.BOTH, expand=True)

        # 增强版Plotly甘特图按钮
        enhanced_btn = tk.Button(
            button_frame,
            text="增强版甘特图 (推荐)\n现代化设计 | 丰富交互 | HTML输出",
            font=("Microsoft YaHei", 12, "bold"),
            bg=SUCCESS_COLOR,
            fg="white",
            relief=tk.FLAT,
            borderwidth=0,
            cursor="hand2",
            padx=20,
            pady=15,
            command=lambda: self.show_enhanced_gantt_chart(selection_window)
        )
        enhanced_btn.pack(fill=tk.X, pady=(0, 15))

        # 传统甘特图按钮
        traditional_btn = tk.Button(
            button_frame,
            text="传统甘特图\n经典界面 | 基础功能 | 窗口显示",
            font=("Microsoft YaHei", 12),
            bg=BUTTON_COLOR,
            fg="white",
            relief=tk.FLAT,
            borderwidth=0,
            cursor="hand2",
            padx=20,
            pady=15,
            command=lambda: self.show_traditional_gantt_chart(selection_window)
        )
        traditional_btn.pack(fill=tk.X, pady=(0, 15))

        # 关闭按钮
        close_btn = tk.Button(
            button_frame,
            text="取消",
            font=("Microsoft YaHei", 11),
            bg=SECONDARY_COLOR,
            fg="white",
            relief=tk.FLAT,
            borderwidth=0,
            cursor="hand2",
            padx=20,
            pady=10,
            command=selection_window.destroy
        )
        close_btn.pack(pady=(20, 0))

    def show_enhanced_gantt_chart(self, parent_window=None):
        """显示增强版Plotly甘特图"""
        if parent_window:
            parent_window.destroy()

        try:
            # 选择文件
            file_path = filedialog.askopenfilename(
                title="选择排程结果Excel文件",
                filetypes=[
                    ("Excel文件", "*.xlsx"),
                    ("Excel文件", "*.xls"),
                    ("所有文件", "*.*")
                ]
            )

            if not file_path:
                return

            # 创建进度窗口
            progress_window = self.create_progress_window("正在生成增强版甘特图...")

            # 在线程中处理
            threading.Thread(
                target=self._create_enhanced_gantt_thread,
                args=(file_path, progress_window),
                daemon=True
            ).start()

        except Exception as e:
            logging.error(f"显示增强版甘特图失败: {str(e)}")
            messagebox.showerror("错误", f"显示增强版甘特图失败: {str(e)}")

    def show_traditional_gantt_chart(self, parent_window=None):
        """显示传统甘特图窗口"""
        if parent_window:
            parent_window.destroy()

        try:
            if not self.gantt_window:
                self.gantt_window = GanttWindow(self.root)
            self.gantt_window.show_window()
        except Exception as e:
            logging.error(f"显示传统甘特图窗口失败: {str(e)}")
            messagebox.showerror("错误", f"显示传统甘特图窗口失败: {str(e)}")

    def _create_enhanced_gantt_thread(self, file_path, progress_window):
        """在线程中创建增强版甘特图"""
        try:
            # 保存当前文件路径
            self._current_file_path = file_path

            # 更新进度
            self.update_progress(progress_window, "正在分析Excel文件结构...")

            # 使用智能表头检测读取Excel数据
            df = self._smart_read_excel(file_path)

            self.update_progress(progress_window, "正在转换数据格式...")

            # 转换数据格式为增强版甘特图所需格式
            gantt_data = self._convert_to_enhanced_gantt_format(df)

            if not gantt_data:
                error_msg = f"没有找到有效的甘特图数据。\n\n请检查Excel文件是否包含以下列：\n• 日期相关列（如：日期、Date、作业日期）\n• 时间相关列（如：起始时间、截止时间、开始时间、结束时间）\n• 任务相关列（如：作业内容、任务）\n• 资源相关列（如：栋别、资源）\n\n当前文件列名：{list(df.columns)}"
                self.root.after(0, lambda: messagebox.showwarning("数据格式错误", error_msg))
                self.root.after(0, progress_window.destroy)
                return

            self.update_progress(progress_window, "正在创建现代化甘特图...")

            # 创建增强版甘特图
            visualizer = EnhancedGanttVisualizer()
            fig = visualizer.create_modern_gantt(gantt_data, "Working Schedule 作业计划甘特图")

            self.update_progress(progress_window, "正在保存HTML文件...")

            # 生成输出文件名
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"enhanced_gantt_chart_{timestamp}.html"

            # 保存为HTML文件
            success = visualizer.save_as_modern_html(output_file, include_controls=True)

            if success:
                self.update_progress(progress_window, "正在打开浏览器...")

                # 在浏览器中打开
                import webbrowser
                import os
                webbrowser.open(f'file://{os.path.abspath(output_file)}')

                # 显示成功消息
                success_msg = f"增强版甘特图已生成并在浏览器中打开！\n文件保存位置: {output_file}"
                self.root.after(0, lambda: messagebox.showinfo("成功", success_msg))

            else:
                self.root.after(0, lambda: messagebox.showerror("错误", "甘特图生成失败"))

            # 关闭进度窗口
            self.root.after(0, progress_window.destroy)

        except Exception as e:
            error_msg = f"创建增强版甘特图失败: {str(e)}"
            logging.error(error_msg)
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
            self.root.after(0, progress_window.destroy)

    def _smart_read_excel(self, file_path):
        """智能读取Excel文件，使用openpyxl直接处理合并单元格"""
        import pandas as pd
        from openpyxl import load_workbook

        logging.info(f"开始智能分析Excel文件: {file_path}")

        # 使用openpyxl直接读取，处理合并单元格
        wb = load_workbook(filename=file_path, data_only=True)
        ws = wb.active

        # 分析合并单元格
        merged_map = {}
        if hasattr(ws, 'merged_cells'):
            for merged_range in ws.merged_cells.ranges:
                min_row, min_col = merged_range.min_row, merged_range.min_col
                max_row, max_col = merged_range.max_row, merged_range.max_col

                for r in range(min_row, max_row + 1):
                    for c in range(min_col, max_col + 1):
                        merged_map[(r, c)] = (min_row, min_col)

        def get_cell_value(row, col):
            """获取单元格值，处理合并单元格"""
            if (row, col) in merged_map:
                main_row, main_col = merged_map[(row, col)]
                return ws.cell(row=main_row, column=main_col).value
            return ws.cell(row=row, column=col).value

        # 转换为DataFrame格式，但保留原始数据
        data = []
        max_row = ws.max_row
        max_col = ws.max_column

        # 智能检测列名行
        header_row = 1  # 默认第1行

        # 检查第1行是否包含典型的列名
        first_row_vals = [get_cell_value(1, col) for col in range(1, min(max_col + 1, 11))]
        first_row_text = ' '.join([str(val) for val in first_row_vals if val])

        # 如果第1行包含典型的列名关键词，使用第1行作为列名
        if any(keyword in first_row_text for keyword in ['日期', '时间', '内容', '栋别', 'Date', 'Time', 'Task']):
            header_row = 1
            data_start_row = 2
        else:
            # 否则使用第2行作为列名
            header_row = 2
            data_start_row = 3

        logging.info(f"使用第{header_row}行作为列名，数据从第{data_start_row}行开始")

        # 获取列名
        headers = []
        for col in range(1, max_col + 1):
            header_val = get_cell_value(header_row, col)
            if header_val:
                headers.append(str(header_val))
            else:
                headers.append(f'Unnamed: {col-1}')

        # 读取数据行
        for row in range(data_start_row, max_row + 1):
            row_data = []
            for col in range(1, max_col + 1):
                cell_val = get_cell_value(row, col)
                row_data.append(cell_val)
            data.append(row_data)

        # 创建DataFrame
        df = pd.DataFrame(data, columns=headers)

        logging.info(f"使用openpyxl读取数据，形状: {df.shape}")
        logging.info(f"列名: {list(df.columns)}")
        logging.info(f"处理了 {len(merged_map)} 个合并单元格")

        return df

    def _parse_complex_gantt_format(self, df):
        """解析复杂的多栋别格式，转换为甘特图数据"""
        gantt_data = []

        # 定义栋别的列范围（基于openpyxl读取的实际列索引）
        building_ranges = {
            'A栋': {'start_col': 2, 'end_col': 9},    # 起始时间到备注
            'B栋': {'start_col': 11, 'end_col': 18},  # 起始时间到备注
            'C栋': {'start_col': 20, 'end_col': 27}   # 起始时间到备注
        }

        current_date = None

        # 扫描所有行
        for index, row in df.iterrows():
            try:
                # 获取日期（第一列）
                date_value = row.iloc[0] if len(row) > 0 else None
                if pd.notna(date_value):
                    if isinstance(date_value, (int, float)) and 40000 < date_value < 50000:
                        # Excel日期格式
                        from datetime import datetime, timedelta
                        excel_date = datetime(1900, 1, 1) + timedelta(days=int(date_value) - 2)
                        current_date = excel_date
                    elif isinstance(date_value, datetime):
                        current_date = date_value

                if not current_date:
                    continue

                # 解析每个栋别的数据
                for building, col_range in building_ranges.items():
                    start_col = col_range['start_col']
                    end_col = col_range['end_col']

                    # 获取时间数据
                    if start_col < len(df.columns) and start_col + 1 < len(df.columns):
                        start_time = row.iloc[start_col]
                        end_time = row.iloc[start_col + 1]

                        if pd.notna(start_time) and pd.notna(end_time):
                            start_str = str(start_time).strip()
                            end_str = str(end_time).strip()

                            # 检查是否是有效的时间格式
                            if ':' in start_str and ':' in end_str:
                                try:
                                    # 解析时间
                                    start_time_obj = datetime.strptime(start_str, '%H:%M').time()
                                    end_time_obj = datetime.strptime(end_str, '%H:%M').time()

                                    # 创建完整的日期时间
                                    start_datetime = datetime.combine(current_date.date(), start_time_obj)
                                    end_datetime = datetime.combine(current_date.date(), end_time_obj)

                                    # 获取任务内容
                                    task_content = ""
                                    if start_col + 5 < len(df.columns):  # 作业内容列
                                        task_val = row.iloc[start_col + 5]
                                        if pd.notna(task_val):
                                            task_content = str(task_val).strip()

                                    if not task_content:
                                        task_content = f"{building}作业"

                                    # 创建甘特图数据项
                                    gantt_data.append({
                                        'Task': task_content,
                                        'Resource': building,
                                        'Start': start_datetime,
                                        'Finish': end_datetime,
                                        'Progress': 0,
                                        'IsOvertime': False,
                                        'Description': f"{building} {start_str}-{end_str}",
                                        'Zone': building,
                                        'Building': building
                                    })

                                except Exception as e:
                                    logging.warning(f"解析时间失败: {start_str}-{end_str}, 错误: {str(e)}")
                                    continue

            except Exception as e:
                logging.warning(f"跳过第{index+1}行数据，解析失败: {str(e)}")
                continue

        logging.info(f"复杂格式解析完成，共解析 {len(gantt_data)} 条甘特图数据")
        return gantt_data

    def _parse_simple_gantt_format(self, df, column_mapping):
        """解析简单格式的甘特图数据"""
        gantt_data = []

        for index, row in df.iterrows():
            try:
                # 获取必要的字段
                if not all(key in column_mapping for key in ['date', 'start_time', 'end_time']):
                    continue

                date_val = row.get(column_mapping['date'])
                start_time = row.get(column_mapping['start_time'])
                end_time = row.get(column_mapping['end_time'])

                if pd.notna(date_val) and pd.notna(start_time) and pd.notna(end_time):
                    # 解析日期和时间
                    if isinstance(date_val, datetime):
                        date_obj = date_val
                    else:
                        date_obj = pd.to_datetime(str(date_val))

                    start_datetime = pd.to_datetime(f"{date_obj.date()} {start_time}")
                    end_datetime = pd.to_datetime(f"{date_obj.date()} {end_time}")

                    # 获取任务内容
                    task_content = row.get(column_mapping.get('task', ''), '未知任务')
                    resource = row.get(column_mapping.get('resource', ''), '默认资源')

                    gantt_data.append({
                        'Task': str(task_content),
                        'Resource': str(resource),
                        'Start': start_datetime,
                        'Finish': end_datetime,
                        'Progress': 0,
                        'IsOvertime': False,
                        'Description': f"{task_content}",
                        'Zone': str(resource),
                        'Building': str(resource)
                    })

            except Exception as e:
                logging.warning(f"跳过第{index+1}行数据，解析失败: {str(e)}")
                continue

        logging.info(f"简单格式解析完成，共解析 {len(gantt_data)} 条甘特图数据")
        return gantt_data

    def _convert_to_enhanced_gantt_format(self, df):
        """转换数据格式为增强版甘特图所需格式"""
        try:
            gantt_data = []

            # 智能识别列名
            column_mapping = self._detect_column_names(df)

            logging.info(f"检测到的列名映射: {column_mapping}")

            # 首先尝试检测数据格式
            logging.info(f"DataFrame列名: {list(df.columns)}")
            logging.info(f"DataFrame形状: {df.shape}")

            # 检查是否包含标准的甘特图列
            has_standard_columns = (
                any('日期' in str(col) or 'Date' in str(col) for col in df.columns) and
                any('时间' in str(col) or 'Time' in str(col) for col in df.columns) and
                any('内容' in str(col) or 'Task' in str(col) or 'Content' in str(col) for col in df.columns)
            )

            # 检查是否是复杂的多栋别格式（列名中包含栋别信息）
            has_multiple_buildings = (
                sum(1 for col in df.columns if '起始时间' in str(col)) > 1 or
                any('A栋起始时间' in str(col) or 'A栋开始时间' in str(col) for col in df.columns) or
                any('B栋起始时间' in str(col) or 'B栋开始时间' in str(col) for col in df.columns) or
                any('C栋起始时间' in str(col) or 'C栋开始时间' in str(col) for col in df.columns) or
                # 检测PPEDL格式的多栋别数据
                any('PPEDL-A栋' in str(col) for col in df.columns) or
                any('PPEDL-B栋' in str(col) for col in df.columns) or
                any('PPEDL-C栋' in str(col) for col in df.columns) or
                # 检测其他多栋别格式
                sum(1 for col in df.columns if 'A栋' in str(col)) > 1 or
                sum(1 for col in df.columns if 'B栋' in str(col)) > 1 or
                sum(1 for col in df.columns if 'C栋' in str(col)) > 1
            )

            # 检查是否是简单的行列格式（每行一个任务）
            has_simple_row_format = (
                len(df.columns) <= 15 and  # 列数不多
                any('日期' in str(col) or 'Date' in str(col) for col in df.columns) and
                any('开始' in str(col) or 'Start' in str(col) or '起始' in str(col) for col in df.columns) and
                any('结束' in str(col) or 'End' in str(col) or '截止' in str(col) for col in df.columns)
            )

            if has_simple_row_format and not has_multiple_buildings:
                logging.info("检测到简单行格式，使用直接解析器")
                return self._parse_simple_row_format(df)
            elif has_multiple_buildings:
                logging.info("检测到多栋别格式，使用复杂解析器")
                # 首先尝试使用process_operations_fixed方法
                gantt_data = self._parse_with_fixed_method()
                if gantt_data:
                    return gantt_data
                else:
                    # 如果失败，尝试使用PPEDL格式解析器
                    logging.info("复杂解析器失败，尝试PPEDL格式解析器")
                    return self._parse_ppedl_format(df)
            else:
                logging.info("使用标准解析器")
                return self._parse_simple_gantt_format(df, column_mapping)

        except Exception as e:
            logging.error(f"数据转换失败: {str(e)}")
            return []

    def _parse_simple_row_format(self, df):
        """解析简单行格式的甘特图数据（每行一个任务）"""
        try:
            gantt_data = []

            # 智能识别列名
            date_col = None
            start_time_col = None
            end_time_col = None
            task_col = None
            building_col = None

            for col in df.columns:
                col_str = str(col).lower()
                if not date_col and ('日期' in col_str or 'date' in col_str):
                    date_col = col
                elif not start_time_col and ('开始' in col_str or 'start' in col_str or '起始' in col_str):
                    start_time_col = col
                elif not end_time_col and ('结束' in col_str or 'end' in col_str or '截止' in col_str):
                    end_time_col = col
                elif not task_col and ('内容' in col_str or 'task' in col_str or 'content' in col_str or '作业' in col_str):
                    task_col = col
                elif not building_col and ('栋别' in col_str or 'building' in col_str or '资源' in col_str):
                    building_col = col

            logging.info(f"识别的列映射: 日期={date_col}, 开始={start_time_col}, 结束={end_time_col}, 任务={task_col}, 栋别={building_col}")

            if not all([date_col, start_time_col, end_time_col, task_col]):
                logging.error("缺少必要的列")
                return []

            for idx, row in df.iterrows():
                try:
                    # 解析日期
                    date_val = row.get(date_col, '')
                    if pd.isna(date_val) or str(date_val) == 'nan':
                        continue

                    # 处理日期格式
                    if isinstance(date_val, datetime):
                        date_obj = date_val
                    elif isinstance(date_val, str):
                        if '/' in date_val:
                            # 6/16 格式
                            parts = date_val.split('/')
                            if len(parts) >= 2:
                                month, day = int(parts[0]), int(parts[1])
                                date_obj = datetime(2025, month, day)
                            else:
                                continue
                        else:
                            # 尝试解析其他日期格式
                            date_obj = pd.to_datetime(date_val)
                    else:
                        # 可能是Excel日期数字
                        date_obj = pd.to_datetime(date_val)

                    # 解析时间
                    start_time_val = row.get(start_time_col, '')
                    end_time_val = row.get(end_time_col, '')

                    # 检查时间是否有效
                    if pd.isna(start_time_val) or pd.isna(end_time_val):
                        continue

                    start_time_str = str(start_time_val).strip()
                    end_time_str = str(end_time_val).strip()

                    if not start_time_str or start_time_str == 'nan' or not end_time_str or end_time_str == 'nan':
                        continue

                    # 解析时间格式
                    try:
                        if ':' in start_time_str and ':' in end_time_str:
                            start_datetime = datetime.combine(date_obj.date(),
                                                            datetime.strptime(start_time_str, '%H:%M').time())
                            end_datetime = datetime.combine(date_obj.date(),
                                                          datetime.strptime(end_time_str, '%H:%M').time())
                        else:
                            logging.warning(f"时间格式不正确: {start_time_str}, {end_time_str}")
                            continue
                    except ValueError as ve:
                        logging.warning(f"时间解析失败: {start_time_str}, {end_time_str}, 错误: {ve}")
                        continue

                    # 获取其他信息
                    task_content = str(row.get(task_col, '未知任务'))
                    building = str(row.get(building_col, '默认')) if building_col else '默认'

                    # 清理栋别名称
                    if building and building != '默认':
                        if not building.endswith('栋'):
                            building = f"{building}栋"

                    gantt_data.append({
                        'Task': task_content,
                        'Resource': building,
                        'Start': start_datetime,
                        'Finish': end_datetime,
                        'Progress': 0,
                        'IsOvertime': False,
                        'Description': f"{building} {task_content}",
                        'Zone': building,
                        'Building': building
                    })

                    logging.info(f"成功解析简单行格式数据: {building} {task_content} {start_datetime} -> {end_datetime}")

                except Exception as e:
                    logging.warning(f"解析第{idx}行时出错: {str(e)}")
                    continue

            logging.info(f"简单行格式解析完成，共解析 {len(gantt_data)} 条甘特图数据")
            return gantt_data

        except Exception as e:
            logging.error(f"简单行格式解析失败: {str(e)}")
            return []

    def _parse_ppedl_format(self, df):
        """解析PPEDL格式的甘特图数据"""
        try:
            gantt_data = []

            logging.info("开始解析PPEDL格式数据")

            # 分析列结构
            columns = list(df.columns)
            logging.info(f"PPEDL格式列名: {columns}")

            # 查找日期列
            date_cols = [i for i, col in enumerate(columns) if '日期' in str(col)]
            if not date_cols:
                logging.error("未找到日期列")
                return []

            date_col_idx = date_cols[0]  # 使用第一个日期列

            # 分析栋别列分布
            building_groups = {}

            # 查找A栋列
            a_cols = [i for i, col in enumerate(columns) if 'PPEDL-A栋' in str(col) or 'A栋' in str(col)]
            if a_cols:
                building_groups['A栋'] = a_cols

            # 查找B栋列
            b_cols = [i for i, col in enumerate(columns) if 'PPEDL-B栋' in str(col) or 'B栋' in str(col)]
            if b_cols:
                building_groups['B栋'] = b_cols

            # 查找C栋列
            c_cols = [i for i, col in enumerate(columns) if 'PPEDL-C栋' in str(col) or 'C栋' in str(col)]
            if c_cols:
                building_groups['C栋'] = c_cols

            logging.info(f"栋别列分组: {building_groups}")

            # 解析每一行数据
            for idx, row in df.iterrows():
                try:
                    # 解析日期
                    date_val = row.iloc[date_col_idx]
                    if pd.isna(date_val) or str(date_val) == 'nan':
                        continue

                    # 处理日期格式
                    if isinstance(date_val, str):
                        if '/' in date_val:
                            # 6/16 格式
                            parts = date_val.split('/')
                            if len(parts) >= 2:
                                month, day = int(parts[0]), int(parts[1])
                                date_obj = datetime(2025, month, day)
                            else:
                                continue
                        else:
                            # 尝试解析其他日期格式
                            date_obj = pd.to_datetime(date_val)
                    else:
                        date_obj = pd.to_datetime(date_val)

                    # 为每个栋别解析数据
                    for building, col_indices in building_groups.items():
                        if len(col_indices) >= 4:  # 至少需要4列：开始时间、结束时间、作业内容、其他
                            try:
                                # 假设列的顺序：开始时间、结束时间、作业内容、其他...
                                start_time_val = row.iloc[col_indices[0]] if len(col_indices) > 0 else None
                                end_time_val = row.iloc[col_indices[1]] if len(col_indices) > 1 else None
                                task_val = row.iloc[col_indices[2]] if len(col_indices) > 2 else None

                                # 检查数据有效性
                                if pd.isna(start_time_val) or pd.isna(end_time_val) or pd.isna(task_val):
                                    continue

                                start_time_str = str(start_time_val).strip()
                                end_time_str = str(end_time_val).strip()
                                task_str = str(task_val).strip()

                                if not start_time_str or start_time_str == 'nan' or not end_time_str or end_time_str == 'nan':
                                    continue

                                # 解析时间
                                if ':' in start_time_str and ':' in end_time_str:
                                    start_datetime = datetime.combine(date_obj.date(),
                                                                    datetime.strptime(start_time_str, '%H:%M').time())
                                    end_datetime = datetime.combine(date_obj.date(),
                                                                  datetime.strptime(end_time_str, '%H:%M').time())

                                    gantt_data.append({
                                        'Task': task_str,
                                        'Resource': building,
                                        'Start': start_datetime,
                                        'Finish': end_datetime,
                                        'Progress': 0,
                                        'IsOvertime': False,
                                        'Description': f"{building} {task_str}",
                                        'Zone': building,
                                        'Building': building
                                    })

                                    logging.info(f"成功解析PPEDL数据: {building} {task_str} {start_datetime} -> {end_datetime}")

                            except Exception as e:
                                logging.warning(f"解析{building}数据时出错: {str(e)}")
                                continue

                except Exception as e:
                    logging.warning(f"解析第{idx}行时出错: {str(e)}")
                    continue

            logging.info(f"PPEDL格式解析完成，共解析 {len(gantt_data)} 条甘特图数据")
            return gantt_data

        except Exception as e:
            logging.error(f"PPEDL格式解析失败: {str(e)}")
            return []

    def _parse_with_fixed_method(self):
        """使用内置的复杂格式解析方法"""
        try:
            # 获取当前处理的文件路径
            file_path = getattr(self, '_current_file_path', None)
            if not file_path:
                logging.error("无法获取当前文件路径")
                return []

            logging.info(f"使用内置复杂格式解析器处理文件: {file_path}")

            # 直接调用现有的process_operations_fixed方法
            import tempfile
            import os

            # 创建临时输出文件
            temp_output = tempfile.mktemp(suffix='.xlsx')

            try:
                # 调用现有的解析函数
                result = process_operations_fixed(input_file=file_path, output_file=temp_output, with_ui=False)

                if result and os.path.exists(temp_output):
                    # 读取解析后的数据
                    import pandas as pd
                    df = pd.read_excel(temp_output)
                    logging.info(f"成功读取解析后的数据: {len(df)} 行")

                    # 转换为甘特图格式
                    gantt_data = []
                    for idx, row in df.iterrows():
                        try:
                            # 解析日期
                            date_val = row.get('日期', '')
                            if pd.isna(date_val) or str(date_val) == 'nan':
                                continue

                            # 处理日期格式
                            if isinstance(date_val, datetime):
                                date_obj = date_val
                            elif isinstance(date_val, str):
                                if '/' in date_val:
                                    # 6/16 格式
                                    month, day = date_val.split('/')
                                    date_obj = datetime(2025, int(month), int(day))
                                else:
                                    # 尝试解析其他日期格式
                                    date_obj = pd.to_datetime(date_val)
                            else:
                                # 可能是Excel日期数字
                                date_obj = pd.to_datetime(date_val)

                            # 解析时间
                            start_time_val = row.get('起始时间', '')
                            end_time_val = row.get('截止时间', '')

                            # 检查时间是否有效
                            if pd.isna(start_time_val) or pd.isna(end_time_val):
                                continue

                            start_time_str = str(start_time_val).strip()
                            end_time_str = str(end_time_val).strip()

                            if not start_time_str or start_time_str == 'nan' or not end_time_str or end_time_str == 'nan':
                                continue

                            # 如果时间字符串包含非时间内容，跳过
                            if not ':' in start_time_str or not ':' in end_time_str:
                                logging.warning(f"时间格式不正确: {start_time_str}, {end_time_str}")
                                continue

                            # 构建完整的开始和结束时间
                            try:
                                start_datetime = datetime.combine(date_obj.date(),
                                                                datetime.strptime(start_time_str, '%H:%M').time())
                                end_datetime = datetime.combine(date_obj.date(),
                                                              datetime.strptime(end_time_str, '%H:%M').time())
                            except ValueError as ve:
                                logging.warning(f"时间解析失败: {start_time_str}, {end_time_str}, 错误: {ve}")
                                continue

                            # 获取其他信息
                            building = str(row.get('栋别', 'Unknown'))
                            content = str(row.get('作业内容', ''))
                            task_type = str(row.get('作业类型', ''))
                            direction = str(row.get('方向', ''))

                            # 清理栋别名称
                            if building and building != 'Unknown':
                                if not building.endswith('栋'):
                                    building = f"{building}栋"

                            gantt_data.append({
                                'Task': content,
                                'Resource': building,
                                'Start': start_datetime,
                                'Finish': end_datetime,
                                'Progress': 0,
                                'IsOvertime': False,
                                'Description': f"{building} {content}",
                                'Zone': building,
                                'Building': building
                            })

                            logging.info(f"成功转换甘特图数据: {building} {content} {start_datetime} -> {end_datetime}")

                        except Exception as e:
                            logging.warning(f"解析第{idx}行时出错: {str(e)}")
                            continue

                    # 清理临时文件
                    try:
                        os.remove(temp_output)
                    except:
                        pass

                    logging.info(f"内置方法解析完成，共解析 {len(gantt_data)} 条甘特图数据")
                    return gantt_data
                else:
                    logging.error("process_operations_fixed解析失败")
                    return []

            except Exception as e:
                logging.error(f"调用process_operations_fixed失败: {str(e)}")
                # 清理临时文件
                try:
                    if os.path.exists(temp_output):
                        os.remove(temp_output)
                except:
                    pass
                return []

        except Exception as e:
            logging.error(f"使用内置方法解析失败: {str(e)}")
            return []

    def _detect_column_names(self, df):
        """智能检测Excel文件的列名"""
        columns = df.columns.tolist()
        logging.info(f"Excel文件列名: {columns}")

        column_mapping = {}

        # 日期列的可能名称
        date_candidates = ['日期', 'Date', '作业日期', '计划日期', '排程日期']
        for col in columns:
            if any(candidate in str(col) for candidate in date_candidates):
                column_mapping['date'] = col
                break

        # 如果没有找到日期列，检查第一列是否是日期数据
        if 'date' not in column_mapping and len(columns) > 0:
            first_col = columns[0]
            # 检查第一列的数据是否像日期
            try:
                sample_data = df[first_col].dropna().head(5)
                for val in sample_data:
                    if isinstance(val, (int, float)) and 40000 < val < 50000:  # Excel日期范围
                        column_mapping['date'] = first_col
                        break
                    elif '/' in str(val) or '-' in str(val):
                        column_mapping['date'] = first_col
                        break
            except:
                pass

        # 开始时间列的可能名称
        start_time_candidates = ['起始时间', '开始时间', 'Start Time', '计划开始时间', '开始', 'StartTime']
        for col in columns:
            if any(candidate in str(col) for candidate in start_time_candidates):
                column_mapping['start_time'] = col
                break

        # 结束时间列的可能名称
        end_time_candidates = ['截止时间', '结束时间', 'End Time', '计划结束时间', '结束', 'EndTime']
        for col in columns:
            if any(candidate in str(col) for candidate in end_time_candidates):
                column_mapping['end_time'] = col
                break

        # 任务内容列的可能名称
        task_candidates = ['作业内容', 'Task', '任务', '工作内容', '内容', '工作']
        for col in columns:
            if any(candidate in str(col) for candidate in task_candidates):
                column_mapping['task'] = col
                break

        # 资源/栋别列的可能名称
        resource_candidates = ['栋别', 'Resource', '资源', '区域', '位置', '栋', '楼栋']
        for col in columns:
            if any(candidate in str(col) for candidate in resource_candidates):
                column_mapping['resource'] = col
                break

        # 方向列的可能名称
        direction_candidates = ['方向', 'Direction', '目的地', '去向']
        for col in columns:
            if any(candidate in str(col) for candidate in direction_candidates):
                column_mapping['direction'] = col
                break

        # 车牌号列的可能名称
        vehicle_candidates = ['车牌号', 'Vehicle', '车牌', '车辆']
        for col in columns:
            if any(candidate in str(col) for candidate in vehicle_candidates):
                column_mapping['vehicle'] = col
                break

        # 备注列的可能名称
        remark_candidates = ['备注', 'Remark', '说明', '注释', 'Note']
        for col in columns:
            if any(candidate in str(col) for candidate in remark_candidates):
                column_mapping['remark'] = col
                break

        # 实际开始时间
        actual_start_candidates = ['实际开始时间', 'Actual Start', '实际开始']
        for col in columns:
            if any(candidate in str(col) for candidate in actual_start_candidates):
                column_mapping['actual_start'] = col
                break

        # 实际结束时间
        actual_end_candidates = ['实际结束时间', 'Actual End', '实际结束']
        for col in columns:
            if any(candidate in str(col) for candidate in actual_end_candidates):
                column_mapping['actual_end'] = col
                break

        return column_mapping

    def _calculate_progress(self, row, column_mapping):
        """计算任务进度"""
        # 检查是否有实际时间数据
        actual_start = row.get(column_mapping.get('actual_start', ''), '')
        actual_end = row.get(column_mapping.get('actual_end', ''), '')

        if actual_start and actual_end and str(actual_start) != 'nan' and str(actual_end) != 'nan':
            return 100  # 已完成
        elif actual_start and str(actual_start) != 'nan':
            return 50   # 进行中
        else:
            return 0    # 未开始

    def _check_overtime(self, start_time, end_time):
        """检查是否超时"""
        # 计算持续时间
        duration = end_time - start_time
        duration_hours = duration.total_seconds() / 3600

        # 如果持续时间超过8小时，认为是超时
        return duration_hours > 8

    def _create_task_description(self, row, column_mapping):
        """创建任务描述"""
        desc_parts = []

        # 添加基本信息
        direction = row.get(column_mapping.get('direction', ''), '')
        if direction and str(direction) != 'nan':
            desc_parts.append(f"方向: {direction}")

        vehicle = row.get(column_mapping.get('vehicle', ''), '')
        if vehicle and str(vehicle) != 'nan':
            desc_parts.append(f"车牌: {vehicle}")

        remark = row.get(column_mapping.get('remark', ''), '')
        if remark and str(remark) != 'nan':
            desc_parts.append(f"备注: {remark}")

        return " | ".join(desc_parts) if desc_parts else "无详细描述"

    def _extract_building_info(self, building_str):
        """提取栋别信息"""
        building_str = building_str.upper()
        if 'A' in building_str:
            return 'A栋'
        elif 'B' in building_str:
            return 'B栋'
        elif 'C' in building_str:
            return 'C栋'
        elif '捷通' in building_str:
            return '捷通'
        else:
            return '其他'

    def create_progress_window(self, title="处理中..."):
        """创建进度窗口"""
        progress_window = tk.Toplevel(self.root)
        progress_window.title("处理进度")
        progress_window.geometry("400x150")
        progress_window.configure(bg=BG_COLOR)
        progress_window.resizable(False, False)

        # 设置窗口图标
        try:
            if os.path.exists("favicon.ico"):
                progress_window.iconbitmap("favicon.ico")
        except:
            pass

        # 居中显示
        progress_window.transient(self.root)
        progress_window.grab_set()

        # 主框架
        main_frame = tk.Frame(progress_window, bg=BG_COLOR)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 标题标签
        title_label = tk.Label(
            main_frame,
            text=title,
            font=("Microsoft YaHei", 12, "bold"),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        title_label.pack(pady=(0, 20))

        # 进度条
        from tkinter import ttk
        progress_bar = ttk.Progressbar(
            main_frame,
            mode='indeterminate',
            length=300
        )
        progress_bar.pack(pady=(0, 10))
        progress_bar.start()

        # 状态标签
        status_label = tk.Label(
            main_frame,
            text="正在初始化...",
            font=("Microsoft YaHei", 10),
            fg=SECONDARY_COLOR,
            bg=BG_COLOR
        )
        status_label.pack()

        # 保存引用以便更新
        progress_window.status_label = status_label
        progress_window.progress_bar = progress_bar

        return progress_window

    def update_progress(self, progress_window, message):
        """更新进度窗口状态"""
        if progress_window and hasattr(progress_window, 'status_label'):
            self.root.after(0, lambda: progress_window.status_label.config(text=message))

    def show_schedule_editor(self):
        """显示排程编辑器窗口"""
        try:
            if not self.editor_window:
                self.editor_window = ScheduleEditorWindow(self.root)
                # 设置回调函数
                self.editor_window.set_save_callback(self.on_editor_save)
                self.editor_window.set_close_callback(self.on_editor_close)
            self.editor_window.show_window()
        except Exception as e:
            logging.error(f"显示排程编辑器失败: {str(e)}")
            messagebox.showerror("错误", f"显示排程编辑器失败: {str(e)}")

    def on_editor_save(self, file_path: str):
        """编辑器保存回调"""
        logging.info(f"排程编辑器保存文件: {file_path}")
        # 可以在这里添加保存后的处理逻辑

    def on_editor_close(self):
        """编辑器关闭回调"""
        self.editor_window = None
        logging.info("排程编辑器已关闭")

    def open_editor_with_file(self, file_path: str):
        """用指定文件打开编辑器"""
        try:
            if not os.path.exists(file_path):
                messagebox.showerror("错误", f"文件不存在: {file_path}")
                return

            # 创建或重用编辑器窗口
            if not self.editor_window:
                self.editor_window = ScheduleEditorWindow(self.root, file_path)
                self.editor_window.set_save_callback(self.on_editor_save)
                self.editor_window.set_close_callback(self.on_editor_close)
            else:
                # 如果编辑器已存在，加载新文件
                self.editor_window.load_file(file_path)

            self.editor_window.show_window()
            logging.info(f"已用文件打开排程编辑器: {file_path}")

        except Exception as e:
            logging.error(f"打开编辑器失败: {str(e)}")
            messagebox.showerror("错误", f"打开编辑器失败: {str(e)}")

    def show_standard_hours_manager(self):
        """显示标准工时管理窗口"""
        try:
            if self.standard_hours_manager:
                self.standard_hours_manager.show_manager_window()
            else:
                messagebox.showerror("错误", "标准工时管理器未初始化")
        except Exception as e:
            logging.error(f"打开标准工时管理器失败: {str(e)}")
            messagebox.showerror("错误", f"打开标准工时管理器失败: {str(e)}")

    def show_jt_processing(self):
        """显示捷通计划处理页面"""
        # 清除主框架
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # 创建捷通计划处理页面
        jt_frame = tk.Frame(self.main_frame, bg=BG_COLOR)
        jt_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_frame = tk.Frame(jt_frame, bg=THEME_COLOR)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = tk.Label(
            title_frame,
            text="🚚 捷通计划处理",
            font=("Arial", 16, "bold"),
            fg=HEADING_COLOR,
            bg=THEME_COLOR,
            padx=10,
            pady=10
        )
        title_label.pack(side=tk.LEFT)
        
        back_btn = tk.Button(
            title_frame,
            text="🔙 返回",
            command=self.show_welcome_page,
            font=("Arial", 10),
            bg=THEME_COLOR,
            fg=HEADING_COLOR,
            activebackground=BUTTON_COLOR,
            activeforeground=HEADING_COLOR,
            bd=0
        )
        back_btn.pack(side=tk.RIGHT, padx=10)
        
        # 内容框架
        content_frame = tk.Frame(jt_frame, bg=BG_COLOR)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 说明文本
        description = tk.Label(
            content_frame,
            text="将原始Excel数据转换为捷通作业计划表，处理入库/出库作业转换。",
            font=("Arial", 12),
            fg=TEXT_COLOR,
            bg=BG_COLOR,
            justify=tk.LEFT,
            wraplength=800
        )
        description.pack(anchor=tk.W, pady=(0, 20))
        
        # 文件选择框架
        file_frame = tk.Frame(content_frame, bg=BG_COLOR)
        file_frame.pack(fill=tk.X, pady=10)
        
        file_label = tk.Label(
            file_frame,
            text="选择Excel文件：",
            font=("Arial", 11),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        file_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.jt_file_path = tk.StringVar()
        file_entry = tk.Entry(
            file_frame,
            textvariable=self.jt_file_path,
            font=("Arial", 11),
            width=50
        )
        file_entry.grid(row=0, column=1, padx=(0, 10))
        
        browse_btn = tk.Button(
            file_frame,
            text="📁 浏览",
            command=lambda: self.browse_file(self.jt_file_path),
            font=("Arial", 11),
            bg=BUTTON_COLOR,
            fg=HEADING_COLOR
        )
        browse_btn.grid(row=0, column=2)
        
        # 处理按钮
        process_btn = tk.Button(
            content_frame,
            text="🚀 开始处理",
            command=self.process_jt_schedule,
            font=("Arial", 12, "bold"),
            bg=BUTTON_COLOR,
            fg=HEADING_COLOR,
            width=15,
            height=2
        )
        process_btn.pack(pady=30)
        
        # 日志框架
        log_frame = tk.Frame(content_frame, bg=BG_COLOR)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        log_label = tk.Label(
            log_frame,
            text="处理日志：",
            font=("Arial", 11),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        log_label.pack(anchor=tk.W)
        
        self.jt_log_text = tk.Text(
            log_frame,
            font=("Courier", 10),
            height=10,
            bg="white",
            fg=TEXT_COLOR
        )
        self.jt_log_text.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = tk.Scrollbar(self.jt_log_text)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.jt_log_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.jt_log_text.yview)
    
    def show_summary_processing(self):
        """显示汇总计划处理页面"""
        # 清除主框架
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # 创建汇总计划处理页面
        summary_frame = tk.Frame(self.main_frame, bg=BG_COLOR)
        summary_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_frame = tk.Frame(summary_frame, bg=THEME_COLOR)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = tk.Label(
            title_frame,
            text="📋 汇总计划处理",
            font=("Arial", 16, "bold"),
            fg=HEADING_COLOR,
            bg=THEME_COLOR,
            padx=10,
            pady=10
        )
        title_label.pack(side=tk.LEFT)
        
        back_btn = tk.Button(
            title_frame,
            text="🔙 返回",
            command=self.show_welcome_page,
            font=("Arial", 10),
            bg=THEME_COLOR,
            fg=HEADING_COLOR,
            activebackground=BUTTON_COLOR,
            activeforeground=HEADING_COLOR,
            bd=0
        )
        back_btn.pack(side=tk.RIGHT, padx=10)
        
        # 内容框架
        content_frame = tk.Frame(summary_frame, bg=BG_COLOR)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 说明文本
        description = tk.Label(
            content_frame,
            text="将原始Excel数据转换为汇总计划表，包含数据分析和图表生成。",
            font=("Arial", 12),
            fg=TEXT_COLOR,
            bg=BG_COLOR,
            justify=tk.LEFT,
            wraplength=800
        )
        description.pack(anchor=tk.W, pady=(0, 20))
        
        # 文件选择框架
        file_frame = tk.Frame(content_frame, bg=BG_COLOR)
        file_frame.pack(fill=tk.X, pady=10)
        
        file_label = tk.Label(
            file_frame,
            text="选择Excel文件：",
            font=("Arial", 11),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        file_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.summary_file_path = tk.StringVar()
        file_entry = tk.Entry(
            file_frame,
            textvariable=self.summary_file_path,
            font=("Arial", 11),
            width=50
        )
        file_entry.grid(row=0, column=1, padx=(0, 10))
        
        browse_btn = tk.Button(
            file_frame,
            text="📁 浏览",
            command=lambda: self.browse_file(self.summary_file_path),
            font=("Arial", 11),
            bg=BUTTON_COLOR,
            fg=HEADING_COLOR
        )
        browse_btn.grid(row=0, column=2)
        
        # 按钮框架
        button_frame = tk.Frame(content_frame, bg=BG_COLOR)
        button_frame.pack(pady=30)

        # 开始汇总处理按钮
        process_btn = tk.Button(
            button_frame,
            text="🚀 开始汇总处理",
            command=self.process_summary_schedule,
            font=("Arial", 12, "bold"),
            bg=BUTTON_COLOR,
            fg=HEADING_COLOR,
            width=18,
            height=2
        )
        process_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 实际工时分析按钮
        analysis_btn = tk.Button(
            button_frame,
            text="📊 实际工时分析",
            command=self.process_actual_hours_analysis,
            font=("Arial", 12, "bold"),
            bg=SUCCESS_COLOR,
            fg=HEADING_COLOR,
            width=18,
            height=2
        )
        analysis_btn.pack(side=tk.LEFT, padx=(10, 0))

        # 标准工时管理按钮
        standard_hours_btn = tk.Button(
            button_frame,
            text="⏱️ 标准工时管理",
            command=self.show_standard_hours_manager,
            font=("Arial", 12, "bold"),
            bg="#FF9500",  # 橙色，表示配置功能
            fg=HEADING_COLOR,
            width=18,
            height=2
        )
        standard_hours_btn.pack(side=tk.LEFT, padx=(10, 0))
        
        # 日志框架
        log_frame = tk.Frame(content_frame, bg=BG_COLOR)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        log_label = tk.Label(
            log_frame,
            text="处理日志：",
            font=("Arial", 11),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        log_label.pack(anchor=tk.W)
        
        self.summary_log_text = tk.Text(
            log_frame,
            font=("Courier", 10),
            height=10,
            bg="white",
            fg=TEXT_COLOR
        )
        self.summary_log_text.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = tk.Scrollbar(self.summary_log_text)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.summary_log_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.summary_log_text.yview)

    def show_heatmap_analysis(self):
        """显示作业热力图分析页面"""
        # 清除主框架
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # 创建热力图分析页面
        heatmap_frame = tk.Frame(self.main_frame, bg=BG_COLOR)
        heatmap_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_frame = tk.Frame(heatmap_frame, bg=THEME_COLOR)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = tk.Label(
            title_frame,
            text="🔥 作业热力图与超时分析",
            font=("Arial", 16, "bold"),
            fg=HEADING_COLOR,
            bg=THEME_COLOR,
            padx=10,
            pady=10
        )
        title_label.pack(side=tk.LEFT)

        back_btn = tk.Button(
            title_frame,
            text="🔙 返回",
            command=self.show_welcome_page,
            font=("Arial", 10),
            bg=THEME_COLOR,
            fg=HEADING_COLOR,
            activebackground=BUTTON_COLOR,
            activeforeground=HEADING_COLOR,
            bd=0
        )
        back_btn.pack(side=tk.RIGHT, padx=10)

        # 内容框架
        content_frame = tk.Frame(heatmap_frame, bg=BG_COLOR)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 说明文本
        description = tk.Label(
            content_frame,
            text="自动分析作业计划表Excel文件，生成作业热力图和超时分析HTML报告。\n支持日历热力图、栋别分布、时间段分析、超时统计等功能。",
            font=("Arial", 12),
            fg=TEXT_COLOR,
            bg=BG_COLOR,
            justify=tk.LEFT,
            wraplength=800
        )
        description.pack(pady=(0, 20))

        # 文件选择框架
        file_frame = tk.Frame(content_frame, bg=BG_COLOR)
        file_frame.pack(fill=tk.X, pady=20)

        # 初始化文件路径变量
        if not hasattr(self, 'heatmap_file_path'):
            self.heatmap_file_path = tk.StringVar()

        file_label = tk.Label(
            file_frame,
            text="选择作业计划表文件：",
            font=("Arial", 11),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        file_label.grid(row=0, column=0, sticky="w", padx=(0, 10))

        file_entry = tk.Entry(
            file_frame,
            textvariable=self.heatmap_file_path,
            font=("Arial", 11),
            width=50
        )
        file_entry.grid(row=0, column=1, padx=(0, 10))

        browse_btn = tk.Button(
            file_frame,
            text="📁 浏览",
            command=lambda: self.browse_file(self.heatmap_file_path),
            font=("Arial", 11),
            bg=BUTTON_COLOR,
            fg=HEADING_COLOR
        )
        browse_btn.grid(row=0, column=2)

        # 处理按钮
        process_btn = tk.Button(
            content_frame,
            text="🔥 生成热力图报告",
            command=self.process_heatmap_analysis,
            font=("Arial", 12, "bold"),
            bg=BUTTON_COLOR,
            fg=HEADING_COLOR,
            width=20,
            height=2
        )
        process_btn.pack(pady=30)

        # 日志框架
        log_frame = tk.Frame(content_frame, bg=BG_COLOR)
        log_frame.pack(fill=tk.BOTH, expand=True)

        log_label = tk.Label(
            log_frame,
            text="处理日志：",
            font=("Arial", 11, "bold"),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        log_label.pack(anchor="w")

        # 创建日志文本框
        self.heatmap_log_text = tk.Text(
            log_frame,
            height=15,
            font=("Consolas", 10),
            bg="white",
            fg="black"
        )
        self.heatmap_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 滚动条
        scrollbar = tk.Scrollbar(self.heatmap_log_text)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.heatmap_log_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.heatmap_log_text.yview)

    def show_analytics_update(self):
        """显示数据分析更新页面"""
        # 清除主框架
        for widget in self.main_frame.winfo_children():
            widget.destroy()
        
        # 创建数据分析更新页面
        analytics_frame = tk.Frame(self.main_frame, bg=BG_COLOR)
        analytics_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_frame = tk.Frame(analytics_frame, bg=THEME_COLOR)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = tk.Label(
            title_frame,
            text="📈 数据分析更新",
            font=("Arial", 16, "bold"),
            fg=HEADING_COLOR,
            bg=THEME_COLOR,
            padx=10,
            pady=10
        )
        title_label.pack(side=tk.LEFT)
        
        back_btn = tk.Button(
            title_frame,
            text="🔙 返回",
            command=self.show_welcome_page,
            font=("Arial", 10),
            bg=THEME_COLOR,
            fg=HEADING_COLOR,
            activebackground=BUTTON_COLOR,
            activeforeground=HEADING_COLOR,
            bd=0
        )
        back_btn.pack(side=tk.RIGHT, padx=10)
        
        # 内容框架
        content_frame = tk.Frame(analytics_frame, bg=BG_COLOR)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 说明文本
        description = tk.Label(
            content_frame,
            text="从已填写了实际数据的Excel表格中更新/生成DataAnalysis工作表及图表。",
            font=("Arial", 12),
            fg=TEXT_COLOR,
            bg=BG_COLOR,
            justify=tk.LEFT,
            wraplength=800
        )
        description.pack(anchor=tk.W, pady=(0, 20))
        
        # 文件选择框架
        file_frame = tk.Frame(content_frame, bg=BG_COLOR)
        file_frame.pack(fill=tk.X, pady=10)
        
        file_label = tk.Label(
            file_frame,
            text="选择已填写实际数据的Excel文件：",
            font=("Arial", 11),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        file_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.analytics_file_path = tk.StringVar()
        file_entry = tk.Entry(
            file_frame,
            textvariable=self.analytics_file_path,
            font=("Arial", 11),
            width=50
        )
        file_entry.grid(row=0, column=1, padx=(0, 10))
        
        browse_btn = tk.Button(
            file_frame,
            text="📁 浏览",
            command=lambda: self.browse_file(self.analytics_file_path),
            font=("Arial", 11),
            bg=BUTTON_COLOR,
            fg=HEADING_COLOR
        )
        browse_btn.grid(row=0, column=2)
        
        # 处理按钮
        process_btn = tk.Button(
            content_frame,
            text="🔄 更新分析图表",
            command=self.update_analytics,
            font=("Arial", 12, "bold"),
            bg=BUTTON_COLOR,
            fg=HEADING_COLOR,
            width=15,
            height=2
        )
        process_btn.pack(pady=30)
        
        # 日志框架
        log_frame = tk.Frame(content_frame, bg=BG_COLOR)
        log_frame.pack(fill=tk.BOTH, expand=True)
        
        log_label = tk.Label(
            log_frame,
            text="处理日志：",
            font=("Arial", 11),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        log_label.pack(anchor=tk.W)
        
        self.analytics_log_text = tk.Text(
            log_frame,
            font=("Courier", 10),
            height=10,
            bg="white",
            fg=TEXT_COLOR
        )
        self.analytics_log_text.pack(fill=tk.BOTH, expand=True)
        
        # 添加滚动条
        scrollbar = tk.Scrollbar(self.analytics_log_text)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.analytics_log_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.analytics_log_text.yview)
    
    def show_help(self):
        """显示帮助页面"""
        # 清除主框架
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # 创建帮助页面
        help_frame = tk.Frame(self.main_frame, bg=BG_COLOR)
        help_frame.pack(fill=tk.BOTH, expand=True)

        # 标题框架
        title_frame = tk.Frame(help_frame, bg=THEME_COLOR)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = tk.Label(
            title_frame,
            text="📖 使用说明",
            font=("Arial", 16, "bold"),
            fg=HEADING_COLOR,
            bg=THEME_COLOR,
            padx=10,
            pady=10
        )
        title_label.pack(side=tk.LEFT)

        # 按钮框架
        button_frame = tk.Frame(title_frame, bg=THEME_COLOR)
        button_frame.pack(side=tk.RIGHT, padx=10)

        # 下载使用说明按钮
        download_btn = tk.Button(
            button_frame,
            text="📥 下载完整说明",
            command=self.download_user_manual,
            font=("Arial", 10),
            bg=SUCCESS_COLOR,
            fg=HEADING_COLOR,
            activebackground="#28A745",
            activeforeground=HEADING_COLOR,
            bd=0,
            padx=10,
            pady=5
        )
        download_btn.pack(side=tk.RIGHT, padx=(0, 10))

        back_btn = tk.Button(
            button_frame,
            text="🔙 返回",
            command=self.show_welcome_page,
            font=("Arial", 10),
            bg=THEME_COLOR,
            fg=HEADING_COLOR,
            activebackground=BUTTON_COLOR,
            activeforeground=HEADING_COLOR,
            bd=0,
            padx=10,
            pady=5
        )
        back_btn.pack(side=tk.RIGHT)

        # 内容框架
        content_frame = tk.Frame(help_frame, bg=BG_COLOR)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 更新的帮助内容
        help_text = """🏭 工作计划排程系统 v1.0.5 使用说明

📋 系统概述
工作计划排程系统是一个综合性的作业计划管理工具，集成了智能排程、可视化编辑、数据分析等功能，帮助用户高效管理和优化工作计划。

🚀 主要功能（按界面按钮顺序）

1. ⏰ 自动时间排程（核心功能）
   - 功能：智能自动填充工作排程时间，支持7种智能推荐方案
   - 特点：默认起始时间08:30，自动考虑午休时间（11:30-12:30）
   - 超过17:30的结束时间会标记为黄色
   - 支持智能填充作业类型、方向、数量等字段
   - 智能推荐：时间优化、负载均衡、无冲突、早完成、资源优化、内容优化、灵活排序
   - 业务逻辑优先级：完成品→待检品→空箱，出库→入库，远距离→近距离

2. ✏️ 排程编辑器（新增功能）
   - 功能：可视化的排程数据编辑界面，支持拖拽调整和数据修改
   - 特点：支持拖拽重新排序、双击编辑、增删改操作
   - 筛选：按日期、栋别筛选，支持搜索功能
   - 导出：支持保存原文件和另存为二维表格格式
   - 二维表格：ABC栋彩色表头，冻结窗格，自动计算星期

3. 🚚 捷通计划处理
   - 功能：根据AB栋作业计划，智能转换为捷通作业计划表
   - 特点：自动处理入库/出库作业转换，智能时间计算和调整

4. 📋 汇总计划处理
   - 功能：将原始Excel数据转换为汇总计划表，包含数据分析和图表生成
   - 特点：自动处理ABC栋数据，包含实际时间填写字段
   - 注意：填写实际开始时间时，建议使用公式=TEXT(A1, "hh:mm")转换时间格式

5. 📊 甘特图可视化
   - 功能：现代化的甘特图展示，支持交互式操作和实时更新
   - 特点：X轴显示日期，Y轴显示建筑物工作内容和时间信息
   - B建筑物区分入库口和出库口区域
   - 支持浏览器中查看交互式图表

6. 📈 数据分析更新
   - 功能：更新已填写实际时间数据的汇总计划Excel表格中的数据分析
   - 特点：无需重新生成整个表格，实时更新分析图表

7. 🎲 生成模拟数据
   - 功能：为测试目的生成模拟的实际时间数据

8. 📖 使用说明
   - 功能：查看系统使用说明，支持下载HTML和Markdown格式完整文档

🏢 建筑物特定规则
- A栋和C栋：不允许内部时间冲突，采用串行排程
- B栋：允许时间冲突，区分出库口和入库口区域，支持并行作业

📊 作业内容特定规则
- JH011-SH空箱：方向为"A栋仓库"，行标记为黄色
- JH011-SH完成品：方向为"广州/武汉"

💡 使用提示
1. 确保Excel文件格式正确，包含必要的列标题
2. 检查源数据的完整性，避免关键字段缺失
3. 使用标准的HH:MM时间格式
4. 处理前建议备份原始数据文件
5. 自动排程后可直接打开编辑器进行手动调整

📥 点击右上角"下载完整说明"按钮获取详细的使用手册。
        """

        # 创建滚动文本框
        text_frame = tk.Frame(content_frame, bg=BG_COLOR)
        text_frame.pack(fill=tk.BOTH, expand=True)

        help_text_widget = tk.Text(
            text_frame,
            font=("Arial", 11),
            wrap=tk.WORD,
            bg=CARD_BG,
            fg=TEXT_COLOR,
            border=1,
            relief=tk.SOLID,
            padx=15,
            pady=15,
            spacing1=3,
            spacing2=2,
            spacing3=3
        )
        help_text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 添加滚动条
        scrollbar = tk.Scrollbar(text_frame, command=help_text_widget.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        help_text_widget.config(yscrollcommand=scrollbar.set)

        help_text_widget.insert(tk.END, help_text)
        help_text_widget.config(state=tk.DISABLED)  # 设为只读

    def download_user_manual(self):
        """下载完整的使用说明文档"""
        try:
            # 使用简单的选择对话框
            choice = messagebox.askyesnocancel(
                "选择下载格式",
                "请选择下载格式：\n\n"
                "• 点击 '是' 下载 HTML格式（推荐）\n"
                "  美观的网页格式，支持浏览器查看\n\n"
                "• 点击 '否' 下载 Markdown格式\n"
                "  纯文本格式，兼容性好\n\n"
                "• 点击 '取消' 退出下载"
            )

            if choice is True:
                # 用户选择HTML格式
                self._download_manual_file("html")
            elif choice is False:
                # 用户选择Markdown格式
                self._download_manual_file("markdown")
            # choice is None 表示用户取消，不做任何操作

        except Exception as e:
            error_msg = f"显示下载选项时出错：{str(e)}"
            logging.error(error_msg)
            messagebox.showerror("错误", error_msg)

    def _download_manual_file(self, format_type):
        """下载指定格式的使用说明文件"""
        try:
            if format_type == "html":
                source_file = "user_manual.html"
                default_name = "工作计划排程系统使用说明.html"
                file_types = [("HTML文件", "*.html"), ("所有文件", "*.*")]
            else:  # markdown
                source_file = "user_manual.md"
                default_name = "工作计划排程系统使用说明.md"
                file_types = [("Markdown文件", "*.md"), ("文本文件", "*.txt"), ("所有文件", "*.*")]

            # 检查源文件是否存在
            if not os.path.exists(source_file):
                messagebox.showerror("错误", f"使用说明文件 {source_file} 不存在，请联系技术支持。")
                return

            # 让用户选择保存位置
            save_path = filedialog.asksaveasfilename(
                title="保存使用说明文档",
                defaultextension=f".{format_type}" if format_type != "markdown" else ".md",
                filetypes=file_types,
                initialfile=default_name
            )

            if save_path:
                # 读取原始文件内容
                with open(source_file, 'r', encoding='utf-8') as source:
                    content = source.read()

                # 写入到用户选择的位置
                with open(save_path, 'w', encoding='utf-8') as target:
                    target.write(content)

                success_msg = f"使用说明已成功保存到：\n{save_path}"
                messagebox.showinfo("下载成功", success_msg)

                # 询问是否打开文件
                if messagebox.askyesno("打开文件", "是否现在打开使用说明文档？"):
                    try:
                        # 尝试用默认程序打开文件
                        if os.name == 'nt':  # Windows
                            os.startfile(save_path)
                        elif os.name == 'posix':  # macOS and Linux
                            if os.uname().sysname == 'Darwin':  # macOS
                                os.system(f'open "{save_path}"')
                            else:  # Linux
                                os.system(f'xdg-open "{save_path}"')
                    except Exception as e:
                        messagebox.showwarning("提示", f"无法自动打开文件，请手动打开：\n{save_path}")

        except Exception as e:
            error_msg = f"下载使用说明时出错：{str(e)}"
            logging.error(error_msg)
            messagebox.showerror("错误", error_msg)

    def show_about(self):
        """显示关于页面"""
        about_text = f"""
工作计划排程系统 v{VERSION}

一个用于处理、分析和优化工作计划的综合应用程序。

功能包括：
- 处理原始Excel数据并生成捷通作业计划表
- 处理原始Excel数据并生成汇总计划表，包含数据分析
- 从已填写实际数据的表格更新数据分析图表
- 生成模拟的实际开始/结束时间数据用于测试

Copyright © 2023
        """
        messagebox.showinfo("关于", about_text)
    
    def browse_file(self, string_var):
        """文件浏览对话框"""
        file_path = filedialog.askopenfilename(
            filetypes=[("Excel文件", "*.xlsx;*.xls")]
        )
        if file_path:
            string_var.set(file_path)

    def process_heatmap_analysis(self):
        """处理作业热力图分析"""
        file_path = self.heatmap_file_path.get().strip()
        if not file_path:
            messagebox.showerror("错误", "请选择作业计划表Excel文件")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("错误", f"文件不存在: {file_path}")
            return

        # 清空日志文本框
        self.heatmap_log_text.delete(1.0, tk.END)

        # 创建自定义的日志处理器，将日志输出到文本框
        class TextHandler(logging.Handler):
            def __init__(self, text_widget):
                super().__init__()
                self.text_widget = text_widget

            def emit(self, record):
                msg = self.format(record)
                def append():
                    self.text_widget.insert(tk.END, msg + '\n')
                    self.text_widget.see(tk.END)
                    self.text_widget.update()
                self.text_widget.after(0, append)

        # 添加文本处理器到日志
        text_handler = TextHandler(self.heatmap_log_text)
        text_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger = logging.getLogger()
        logger.addHandler(text_handler)

        def run_analysis():
            try:
                self.heatmap_log_text.after(0, lambda: self.heatmap_log_text.insert(tk.END, "开始作业热力图分析...\n"))

                # 创建热力图分析器
                analyzer = WorkHeatmapAnalyzer()

                # 执行完整的分析和报告生成流程
                report_path = analyzer.analyze_and_generate_report(file_path)

                # 显示成功消息
                success_msg = f"热力图报告生成成功！\n报告文件: {report_path}"
                self.root.after(0, lambda: messagebox.showinfo("成功", success_msg))

                # 询问是否打开报告
                def ask_open_report():
                    if messagebox.askyesno("打开报告", "是否立即打开生成的热力图报告？"):
                        try:
                            import webbrowser
                            webbrowser.open(f"file://{os.path.abspath(report_path)}")
                        except Exception as e:
                            messagebox.showerror("错误", f"无法打开报告文件: {e}")

                self.root.after(0, ask_open_report)

            except Exception as e:
                error_msg = f"热力图分析失败: {str(e)}"
                logging.error(error_msg)
                self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
            finally:
                # 移除文本处理器
                logger.removeHandler(text_handler)

        # 在新线程中运行分析
        import threading
        analysis_thread = threading.Thread(target=run_analysis)
        analysis_thread.daemon = True
        analysis_thread.start()

    def process_jt_schedule(self):
        """处理捷通计划调度"""
        file_path = self.jt_file_path.get().strip()
        if not file_path:
            messagebox.showerror("错误", "请选择Excel文件")
            return
        
        # 清空日志文本框
        self.jt_log_text.delete(1.0, tk.END)
        
        # 创建自定义的日志处理器，将日志输出到文本框
        log_handler = TextHandler(self.jt_log_text)
        log_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        log_handler.setFormatter(formatter)
        
        # 获取根日志记录器并添加处理器
        root_logger = logging.getLogger()
        root_logger.addHandler(log_handler)
        
        # 在新线程中运行处理函数
        thread = threading.Thread(target=self.run_jt_processing, args=(file_path,))
        thread.daemon = True
        thread.start()
    
    def run_jt_processing(self, file_path):
        """在线程中运行捷通计划处理 - 将AB栋数据转换为捷通作业计划"""
        try:
            # 记录开始处理
            logging.info(f"开始处理文件: {file_path}")
            
            # 加载工作簿
            wb = load_workbook(filename=file_path, data_only=True, read_only=False)
            
            # 检查工作表
            ws = wb.active
            logging.info(f"工作表名: {ws.title}, 大小: {ws.max_row}行 x {ws.max_column}列")
            
            # 打印表格内容预览，帮助调试
            logging.info("分析表格内容中...")
            
            # 分析合并单元格
            merged_map = analyze_merged_cells(ws)
            logging.info(f"分析到 {len(merged_map)} 个合并单元格位置")
            
            # 设置A栋和B栋的固定列映射，基于预览观察得出
            # 根据预览内容设定列索引（Excel列索引从1开始）
            a_columns = {
                'date': 0,  # 日期，第1列
                'weekday': 1,  # 星期，第2列
                'start_time': 2,  # 起始时间，第3列
                'end_time': 3,  # 截止时间，第4列
                'op_time': 4,  # 作业时长，第5列
                'op_type': 5,  # 作业类型，第6列
                'direction': 6,  # 方向，第7列
                'content': 7,  # 作业内容，第8列
                'quantity': 8,  # 数量，第9列
                'remark': 9  # 备注，第10列
            }

            b_columns = {
                'date': 0,  # 日期，共用A栋的第1列
                'weekday': 1,  # 星期，共用A栋的第2列
                'in_out': 10,  # 出/入，第11列
                'start_time': 11,  # 起始时间，第12列
                'end_time': 12,  # 截止时间，第13列
                'op_time': 13,  # 作业时长，第14列
                'op_type': 14,  # 作业类型，第15列
                'direction': 15,  # 方向，第16列
                'content': 16,  # 作业内容，第17列
                'quantity': 17,  # 数量，第18列
                'remark': 18  # 备注，第19列
            }
            
            logging.info("开始处理AB栋数据，转换为捷通作业计划...")
            
            # 处理数据
            rows_data = []
            current_date = None
            current_weekday = None
            current_b_in_out = None  # 用于记录当前B栋的出/入库口状态

            # 全表扫描获取所有数据，暂不筛选
            all_rows_data = []

            # 扫描所有行，提取数据
            # 扩展扫描范围到9999行，确保不遗漏任何数据
            max_scan_row = max(ws.max_row, 9999)
            for row_idx in range(3, max_scan_row + 1):  # 从第3行开始，跳过表头
                try:
                    # 处理日期和星期合并单元格
                    date_value = get_cell_value(ws, row_idx, a_columns['date'] + 1, merged_map)
                    if date_value:
                        current_date = date_value

                    weekday_value = get_cell_value(ws, row_idx, a_columns['weekday'] + 1, merged_map)
                    if weekday_value:
                        current_weekday = weekday_value

                    # 处理B栋的出/入库口状态
                    b_in_out_value = get_cell_value(ws, row_idx, b_columns['in_out'] + 1, merged_map)
                    if b_in_out_value:
                        current_b_in_out = b_in_out_value

                    # 收集A栋数据 - 不进行筛选，收集所有有效数据
                    a_op_type = get_cell_value(ws, row_idx, a_columns['op_type'] + 1, merged_map)
                    a_content = get_cell_value(ws, row_idx, a_columns['content'] + 1, merged_map)
                    a_direction = get_cell_value(ws, row_idx, a_columns['direction'] + 1, merged_map)
                    a_start_time = get_cell_value(ws, row_idx, a_columns['start_time'] + 1, merged_map)
                    a_end_time = get_cell_value(ws, row_idx, a_columns['end_time'] + 1, merged_map)

                    # 只要有一个核心字段有值，就认为是有效行数据
                    if any([a_op_type, a_content, a_direction, a_start_time, a_end_time]):
                        row_data = {
                            '栋别': 'A',
                            '日期': current_date,
                            '星期': current_weekday,
                            '起始时间': a_start_time,
                            '截止时间': a_end_time,
                            '作业时间': get_cell_value(ws, row_idx, a_columns['op_time'] + 1, merged_map),
                            '作业类型': a_op_type or "未指定",
                            '方向': a_direction or "未指定",
                            '作业内容': a_content or "未指定",
                            '数量': get_cell_value(ws, row_idx, a_columns['quantity'] + 1, merged_map),
                            '备注': get_cell_value(ws, row_idx, a_columns['remark'] + 1, merged_map),
                            '行号': row_idx
                        }
                        all_rows_data.append(row_data)

                    # 收集B栋数据 - 不进行筛选，收集所有有效数据
                    b_op_type = get_cell_value(ws, row_idx, b_columns['op_type'] + 1, merged_map)
                    b_content = get_cell_value(ws, row_idx, b_columns['content'] + 1, merged_map)
                    b_direction = get_cell_value(ws, row_idx, b_columns['direction'] + 1, merged_map)
                    b_start_time = get_cell_value(ws, row_idx, b_columns['start_time'] + 1, merged_map)
                    b_end_time = get_cell_value(ws, row_idx, b_columns['end_time'] + 1, merged_map)

                    # 增强B栋作业类型判断 - 如果B栋作业类型为空，尝试根据出/入库口状态推断
                    if not b_op_type and current_b_in_out:
                        if "出库" in str(current_b_in_out):
                            b_op_type = "出库"
                        elif "入库" in str(current_b_in_out):
                            b_op_type = "入库"

                    # 只要有一个核心字段有值，就认为是有效行数据
                    if any([b_op_type, b_content, b_direction, b_start_time, b_end_time]):
                        row_data = {
                            '栋别': 'B',
                            '日期': current_date,
                            '星期': current_weekday,
                            '起始时间': b_start_time,
                            '截止时间': b_end_time,
                            '作业时间': get_cell_value(ws, row_idx, b_columns['op_time'] + 1, merged_map),
                            '作业类型': b_op_type or "未指定",
                            '方向': b_direction or "未指定",
                            '作业内容': b_content or "未指定",
                            '数量': get_cell_value(ws, row_idx, b_columns['quantity'] + 1, merged_map),
                            '备注': get_cell_value(ws, row_idx, b_columns['remark'] + 1, merged_map),
                            '出入库口': current_b_in_out,
                            '行号': row_idx
                        }
                        all_rows_data.append(row_data)

                except Exception as e:
                    logging.error(f"处理第{row_idx}行时出错: {str(e)}")
                    logging.error(traceback.format_exc())

            logging.info(f"共收集到 {len(all_rows_data)} 条原始记录")

            # 现在再单独进行筛选
            for row in all_rows_data:
                building = row['栋别']
                op_type = row['作业类型']
                content = row['作业内容']
                direction = row['方向']

                include, reason = should_include_record(op_type, content, direction, building)
                if include:
                    # 标记时间是否为空
                    start_time = row['起始时间']
                    end_time = row['截止时间']
                    row['时间为空'] = not start_time or not end_time

                    # 添加筛选原因
                    row['筛选原因'] = reason

                    # 添加到最终数据集
                    rows_data.append(row)
                    logging.info(f"选中{building}栋记录：行 {row['行号']}, 类型: {op_type}, 内容: {content}, 原因: {reason}")

            logging.info(f"总共找到 {len(rows_data)} 条满足条件的记录")

            # 创建转换后的数据
            transformed_data = []

            for row in rows_data:
                try:
                    # 处理日期 - 确保格式为m/d
                    formatted_date = convert_excel_date(row['日期'])

                    # 如果时间为空，创建一条带警告的记录
                    if row.get('时间为空', False):
                        new_row = {
                            '日期': formatted_date,
                            '栋别': row['栋别'],
                            '起始时间': "需检查",
                            '截止时间': "需检查",
                            '作业时长': 0.5,
                            '作业类型': '入库' if '出库' in str(row['作业类型']) else '出库',
                            '方向': 'PPEDL工厂' if '入库' in str(row['作业类型']) else '捷通',
                            '作业内容': row['作业内容'],
                            '数量': row['数量'],
                            '备注': f"{row['备注'] if row['备注'] else ''} 检查源表格中数据[行{row['行号']}]".strip(),
                            '实际开始时间': '',
                            '实际结束时间': '',
                            '实际作业时长': '',
                            '异常原因': '源数据时间缺失'
                        }
                        transformed_data.append(new_row)
                        logging.info(f"已转换(时间为空): 栋别:{row['栋别']}, 日期:{formatted_date}, 行号:{row['行号']}")
                        continue

                    # 处理时间
                    start_time = row['起始时间']
                    end_time = row['截止时间']

                    # 判断是否为特殊处理的情况："入库"且包含特定内容
                    is_special_case = ('入库' in str(row['作业类型']).strip() and 
                                      ('jh027-sd周转箱' in normalize_text(row['作业内容']) or 
                                       'jh027-sc周转箱' in normalize_text(row['作业内容'])))

                    # 解析时间
                    if is_special_case:
                        # 使用起始时间作为基准
                        time_str = str(start_time).strip() if start_time else None
                        if isinstance(start_time, datetime):
                            base_dt = start_time
                        elif time_str:
                            # 尝试解析时间字符串
                            base_dt = parse_time_string(time_str)
                            # 如果失败，进一步尝试解析
                            if not base_dt and ':' in time_str:
                                parts = time_str.split(':')
                                if len(parts) >= 2:
                                    try:
                                        hour = int(parts[0])
                                        minute = int(parts[1])
                                        base_dt = datetime(1900, 1, 1, hour, minute)
                                    except:
                                        pass
                        else:
                            base_dt = None
                    else:
                        # 使用截止时间作为基准（原逻辑）
                        time_str = str(end_time).strip() if end_time else None
                        if isinstance(end_time, datetime):
                            base_dt = end_time
                        elif time_str:
                            # 尝试解析时间字符串
                            base_dt = parse_time_string(time_str)
                            # 如果失败，进一步尝试解析
                            if not base_dt and ':' in time_str:
                                parts = time_str.split(':')
                                if len(parts) >= 2:
                                    try:
                                        hour = int(parts[0])
                                        minute = int(parts[1])
                                        base_dt = datetime(1900, 1, 1, hour, minute)
                                    except:
                                        pass
                        else:
                            base_dt = None

                    if not base_dt:
                        logging.warning(f"无法解析时间 {start_time if is_special_case else end_time} (类型: {type(start_time if is_special_case else end_time)})，跳过此行 {row['行号']}")
                        continue

                    # 根据作业内容确定作业时长
                    duration = get_operation_duration(row['作业内容'])
                    
                    # 计算新时间
                    if is_special_case:
                        # 新规则：向前推算
                        new_end_dt = base_dt - timedelta(minutes=30)
                        new_start_dt = new_end_dt - timedelta(hours=duration)
                    else:
                        # 原规则：向后推算
                        new_start_dt = base_dt + timedelta(minutes=30)
                        new_end_dt = new_start_dt + timedelta(hours=duration)

                    # 应用新的时间调整规则
                    new_start_dt, new_end_dt = apply_time_adjustment_rules(new_start_dt, new_end_dt, duration)

                    # 时间格式化
                    new_start_time = new_start_dt.strftime('%H:%M')
                    new_end_time = new_end_dt.strftime('%H:%M')

                    # 转换作业类型和方向
                    op_type = row['作业类型']

                    # 标准化作业类型字符串
                    op_type_str = str(op_type).strip() if op_type else "入库"  # 默认入库

                    # 判断原始作业类型
                    if '入库' in op_type_str:
                        new_op_type = '出库'
                    else:
                        new_op_type = '入库'

                    # 设置新方向
                    new_direction = 'PPEDL工厂' if new_op_type == '出库' else '捷通'

                    # 创建新行数据
                    new_row = {
                        '日期': formatted_date,
                        '栋别': row['栋别'],
                        '起始时间': new_start_time,
                        '截止时间': new_end_time,
                        '作业时长': duration,
                        '作业类型': new_op_type,
                        '方向': new_direction,
                        '作业内容': row['作业内容'],
                        '数量': row['数量'],
                        '备注': row['备注'],
                        '实际开始时间': '',
                        '实际结束时间': '',
                        '实际作业时长': '',
                        '异常原因': ''
                    }
                    transformed_data.append(new_row)
                    logging.info(f"已转换: 栋别:{row['栋别']}, 日期:{formatted_date}, 行号:{row['行号']}, 原类型:{op_type_str} -> 新类型:{new_op_type}")
                except Exception as e:
                    logging.error(f"转换数据时出错: {str(e)}")
                    logging.error(traceback.format_exc())

            # 按日期和起始时间排序（从早到晚）
            transformed_data.sort(key=lambda x: (
                parse_date_for_sorting(x['日期']),
                parse_time_for_sorting(x['起始时间'])
            ))

            # 创建新工作簿保存结果
            new_wb = Workbook()
            new_ws = new_wb.active
            new_ws.title = "捷通作业计划"
            
            # 创建DataFrame并输出到Excel
            if transformed_data:
                # 确保所有字段都出现在每条记录中(防止缺失导致的列不对齐)
                all_fields = [
                    '日期', '栋别', '起始时间', '截止时间', '作业时长', 
                    '作业类型', '方向', '作业内容', '数量', '备注',
                    '实际开始时间', '实际结束时间', '实际作业时长', '异常原因'
                ]
                
                # 确保每条记录都包含所有字段
                for record in transformed_data:
                    for field in all_fields:
                        if field not in record:
                            record[field] = ''
                
                # 创建DataFrame并指定列顺序
                df = pd.DataFrame(transformed_data)[all_fields]
                
                # 选择保存位置
                default_output = "作业计划表捷通.xlsx"
                output_file = filedialog.asksaveasfilename(
                    title="选择保存位置",
                    filetypes=[("Excel文件", "*.xlsx")],
                    defaultextension=".xlsx",
                    initialfile=default_output
                )
                
                if not output_file:
                    logging.info("用户取消了保存操作")
                    self.root.after(0, lambda: messagebox.showinfo("提示", "操作已取消"))
                    return
                
                # 保存到Excel
                with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                    df.to_excel(writer, index=False, sheet_name='Sheet1')
                    
                    # 获取工作簿并美化表格
                    workbook = writer.book
                    apply_table_style(workbook, 'Sheet1')
                    
                    # 保存更改
                    workbook.save(output_file)
                
                success_msg = f"处理完成，成功转换 {len(transformed_data)} 条记录，结果已保存到 {output_file}"
                logging.info(success_msg)
                
                # 显示成功消息
                self.root.after(0, lambda: messagebox.showinfo("成功", success_msg))
            else:
                no_data_msg = "没有找到符合条件的数据"
                logging.info(no_data_msg)
                self.root.after(0, lambda: messagebox.showwarning("提示", no_data_msg))
            
        except Exception as e:
            error_msg = f"处理过程中出错: {str(e)}"
            logging.error(error_msg)
            logging.error(traceback.format_exc())
            # 显示错误消息
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
    
    def process_summary_schedule(self):
        """处理汇总计划调度"""
        file_path = self.summary_file_path.get().strip()
        if not file_path:
            messagebox.showerror("错误", "请选择Excel文件")
            return
        
        # 清空日志文本框
        self.summary_log_text.delete(1.0, tk.END)
        
        # 创建自定义的日志处理器，将日志输出到文本框
        log_handler = TextHandler(self.summary_log_text)
        log_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        log_handler.setFormatter(formatter)
        
        # 获取根日志记录器并添加处理器
        root_logger = logging.getLogger()
        root_logger.addHandler(log_handler)
        
        # 在新线程中运行处理函数
        thread = threading.Thread(target=self.run_summary_processing, args=(file_path,))
        thread.daemon = True
        thread.start()
    
    def run_summary_processing(self, file_path):
        """在线程中运行汇总计划处理"""
        try:
            # 记录开始处理
            logging.info(f"开始处理文件: {file_path}")

            # 调用处理函数，不需要指定output_file，函数内部会提示用户选择
            output_file = process_operations_fixed(file_path, with_ui=True)

            if output_file:
                success_msg = f"处理完成，结果已保存到 {output_file}"
                logging.info(success_msg)
                # 显示成功消息
                self.root.after(0, lambda: messagebox.showinfo("成功", success_msg))

        except Exception as e:
            logging.error(f"处理过程中出错: {str(e)}")
            logging.error(traceback.format_exc())
            # 显示错误消息
            self.root.after(0, lambda: messagebox.showerror("错误", f"处理过程中出错: {str(e)}"))

    def process_actual_hours_analysis(self):
        """处理实际工时分析"""
        file_path = self.summary_file_path.get().strip()
        if not file_path:
            messagebox.showerror("错误", "请选择Excel文件")
            return

        # 清空日志文本框
        self.summary_log_text.delete(1.0, tk.END)

        # 创建自定义的日志处理器，将日志输出到文本框
        log_handler = TextHandler(self.summary_log_text)
        log_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        log_handler.setFormatter(formatter)

        # 获取根日志记录器并添加处理器
        root_logger = logging.getLogger()
        root_logger.addHandler(log_handler)

        # 在新线程中运行处理函数
        thread = threading.Thread(target=self.run_actual_hours_analysis, args=(file_path,))
        thread.daemon = True
        thread.start()

    def run_actual_hours_analysis(self, file_path):
        """在线程中运行实际工时分析"""
        try:
            # 记录开始处理
            logging.info(f"开始实际工时分析: {file_path}")

            # 调用实际工时分析函数
            output_file = self.analyze_actual_working_hours(file_path)

            if output_file:
                success_msg = f"实际工时分析完成，结果已保存到 {output_file}"
                logging.info(success_msg)
                # 显示成功消息
                self.root.after(0, lambda: messagebox.showinfo("成功", success_msg))

        except Exception as e:
            logging.error(f"实际工时分析过程中出错: {str(e)}")
            logging.error(traceback.format_exc())
            # 显示错误消息
            self.root.after(0, lambda: messagebox.showerror("错误", f"实际工时分析过程中出错: {str(e)}"))

    def analyze_actual_working_hours(self, input_file):
        """实际工时分析核心函数"""
        try:
            import pandas as pd
            from datetime import datetime, time
            import os
            from tkinter import filedialog
            import openpyxl
            from openpyxl.styles import PatternFill, Font, Border, Side, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows

            logging.info("开始读取Excel文件...")

            # 使用openpyxl读取文件以保持格式
            workbook = openpyxl.load_workbook(input_file)
            worksheet = workbook.active

            # 转换为DataFrame进行数据处理
            data = []
            headers = []
            for row in worksheet.iter_rows(values_only=True):
                if not headers:
                    headers = list(row)
                else:
                    data.append(list(row))

            df = pd.DataFrame(data, columns=headers)
            logging.info(f"成功读取文件，共 {len(df)} 行数据")

            # 验证必需的列是否存在
            required_columns = ['作业时长', '实际开始时间', '实际结束时间']
            missing_columns = []

            # 检查列是否存在（按列名检查）
            for col in required_columns:
                if col not in df.columns:
                    missing_columns.append(col)

            if missing_columns:
                error_msg = f"缺少必需的列: {', '.join(missing_columns)}"
                logging.error(error_msg)
                raise ValueError(error_msg)

            logging.info("开始计算实际工时...")

            # 确保目标列存在
            if '实际作业时长（不含午休，休息，晚餐时长）' not in df.columns:
                df['实际作业时长（不含午休，休息，晚餐时长）'] = None
            if '超作业标准工时(分)' not in df.columns:
                df['超作业标准工时(分)'] = None

            # 计算每行的实际工时
            processed_count = 0
            for index, row in df.iterrows():
                try:
                    # 获取实际开始和结束时间
                    actual_start = self.parse_time_value(row['实际开始时间'])
                    actual_end = self.parse_time_value(row['实际结束时间'])

                    # 获取标准工时 - 优先使用标准工时管理器，否则使用Excel中的作业时长
                    standard_duration = None
                    if self.standard_hours_manager and '作业内容' in row:
                        # 从标准工时管理器获取标准工时
                        content = row['作业内容']
                        standard_duration = self.standard_hours_manager.get_standard_hours(content)
                        if standard_duration:
                            logging.info(f"第{index+1}行: 从标准工时管理器获取 '{content}' 的标准工时: {standard_duration}分钟")

                    # 如果标准工时管理器没有找到，尝试从Excel的作业时长列获取
                    if standard_duration is None and '作业时长' in row:
                        standard_duration = self.parse_duration_value(row['作业时长'])
                        if standard_duration:
                            logging.info(f"第{index+1}行: 从Excel文件获取标准工时: {standard_duration}分钟")

                    if actual_start is not None and actual_end is not None:
                        # 计算实际作业时长（扣除休息时间）
                        actual_duration = self.calculate_actual_working_duration(actual_start, actual_end)
                        formatted_duration = self.format_duration_to_hm(actual_duration)
                        df.at[index, '实际作业时长（不含午休，休息，晚餐时长）'] = formatted_duration

                        logging.info(f"第{index+1}行: 开始={actual_start}, 结束={actual_end}, 实际时长={actual_duration}分钟, 格式化={formatted_duration}")

                        # 计算超时分钟数
                        if standard_duration is not None:
                            overtime_minutes = actual_duration - standard_duration
                            formatted_overtime = self.format_overtime_to_hm(overtime_minutes)
                            df.at[index, '超作业标准工时(分)'] = formatted_overtime
                            logging.info(f"第{index+1}行: 标准时长={standard_duration}分钟, 超时={overtime_minutes}分钟, 格式化={formatted_overtime}")
                        else:
                            logging.warning(f"第{index+1}行: 无法获取标准时长，无法计算超时")
                    else:
                        logging.warning(f"第{index+1}行: 开始或结束时间为空 - 开始={actual_start}, 结束={actual_end}")

                    processed_count += 1

                except Exception as e:
                    logging.warning(f"第 {index + 1} 行数据处理失败: {str(e)}")
                    continue

            logging.info(f"数据计算完成，处理了 {processed_count} 行数据")

            logging.info("工时计算完成，准备保存文件...")

            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            output_dir = os.path.dirname(input_file)
            output_file = os.path.join(output_dir, f"{base_name}_实际工时分析.xlsx")

            # 如果文件已存在，自动生成新的文件名
            if os.path.exists(output_file):
                import time
                timestamp = int(time.time())
                output_file = os.path.join(output_dir, f"{base_name}_实际工时分析_{timestamp}.xlsx")
                logging.info(f"文件已存在，使用新文件名: {os.path.basename(output_file)}")

            logging.info(f"准备保存到: {output_file}")

            # 更新工作表数据，保持原有格式
            # 找到新增列的位置
            actual_duration_col = '实际作业时长（不含午休，休息，晚餐时长）'
            overtime_col = '超作业标准工时(分)'

            # 检查新列是否已存在
            actual_col_idx = None
            overtime_col_idx = None

            for i, header in enumerate(headers):
                if header == actual_duration_col:
                    actual_col_idx = i + 1  # Excel列从1开始
                elif header == overtime_col:
                    overtime_col_idx = i + 1

            # 如果列不存在，添加到末尾
            if actual_col_idx is None:
                actual_col_idx = len(headers) + 1
                worksheet.cell(row=1, column=actual_col_idx, value=actual_duration_col)
                headers.append(actual_duration_col)

            if overtime_col_idx is None:
                overtime_col_idx = len(headers) + 1
                worksheet.cell(row=1, column=overtime_col_idx, value=overtime_col)
                headers.append(overtime_col)

            logging.info(f"列位置: 实际工时列={actual_col_idx}, 超时列={overtime_col_idx}")

            # 复制表头样式到新列
            def copy_cell_style(source_cell, target_cell, is_overtime_positive=False):
                """完整的样式复制，包括边框和条件格式化"""
                try:
                    from openpyxl.styles import PatternFill, Font, Alignment, Border, Side

                    # 复制填充样式
                    if is_overtime_positive:
                        # 超时情况使用黄色背景
                        target_cell.fill = PatternFill(
                            start_color="FFFF00",  # 黄色
                            fill_type="solid"
                        )
                    else:
                        # 使用白色背景，与其他列保持一致
                        target_cell.fill = PatternFill(
                            start_color="FFFFFF",  # 白色背景
                            fill_type="solid"
                        )

                    # 复制字体样式
                    if hasattr(source_cell, 'font') and source_cell.font:
                        try:
                            target_cell.font = Font(
                                name=source_cell.font.name or "Calibri",
                                size=source_cell.font.size or 11,
                                bold=source_cell.font.bold or False,
                                color="000000"  # 黑色字体
                            )
                        except:
                            pass

                    # 复制对齐样式
                    try:
                        target_cell.alignment = Alignment(
                            horizontal="center",
                            vertical="center"
                        )
                    except:
                        pass

                    # 复制边框样式
                    try:
                        if hasattr(source_cell, 'border') and source_cell.border:
                            # 创建统一的边框样式
                            thin_border = Side(border_style="thin", color="000000")
                            target_cell.border = Border(
                                left=thin_border,
                                right=thin_border,
                                top=thin_border,
                                bottom=thin_border
                            )
                    except:
                        pass

                except Exception as e:
                    # 静默处理样式错误，不影响主要功能
                    pass

            if worksheet.max_column >= 1:
                header_style = worksheet.cell(row=1, column=1)
                new_col_n = worksheet.cell(row=1, column=actual_col_idx)
                new_col_o = worksheet.cell(row=1, column=overtime_col_idx)

                # 复制表头样式（表头不需要条件格式化）
                copy_cell_style(header_style, new_col_n, is_overtime_positive=False)
                copy_cell_style(header_style, new_col_o, is_overtime_positive=False)

            # 填充数据并复制行样式
            logging.info(f"开始填充数据，DataFrame列名: {list(df.columns)}")
            logging.info(f"DataFrame形状: {df.shape}")

            # 检查数据是否存在
            actual_duration_col = '实际作业时长（不含午休，休息，晚餐时长）'
            overtime_col = '超作业标准工时(分)'

            actual_data_count = df[actual_duration_col].notna().sum()
            overtime_data_count = df[overtime_col].notna().sum()
            logging.info(f"实际工时数据行数: {actual_data_count}, 超时数据行数: {overtime_data_count}")

            # 显示前几行数据用于调试
            for i in range(min(3, len(df))):
                logging.info(f"第{i+1}行数据: 实际工时={df.iloc[i][actual_duration_col]}, 超时={df.iloc[i][overtime_col]}")

            for index, row in df.iterrows():
                row_num = index + 2  # Excel行号从1开始，第1行是标题

                actual_duration_value = row.get(actual_duration_col)
                overtime_value = row.get(overtime_col)

                # 填充实际工时数据
                if pd.notna(actual_duration_value):
                    actual_cell = worksheet.cell(row=row_num, column=actual_col_idx, value=actual_duration_value)
                    logging.info(f"✅ 填充实际工时到 ({row_num}, {actual_col_idx}): {actual_duration_value}")
                else:
                    logging.warning(f"❌ 第{row_num}行实际工时为空: {actual_duration_value}")

                # 填充超时数据并应用条件格式化
                if pd.notna(overtime_value):
                    overtime_cell = worksheet.cell(row=row_num, column=overtime_col_idx, value=overtime_value)

                    # 检查是否为超时情况（正数）
                    is_overtime = False
                    try:
                        # 解析时间格式，检查是否为正数
                        if isinstance(overtime_value, str) and overtime_value.strip():
                            if not overtime_value.startswith('-') and overtime_value != '0:00':
                                is_overtime = True
                    except:
                        pass

                    logging.info(f"✅ 填充超时到 ({row_num}, {overtime_col_idx}): {overtime_value} {'(超时-黄色)' if is_overtime else ''}")
                else:
                    is_overtime = False
                    logging.warning(f"❌ 第{row_num}行超时为空: {overtime_value}")

                # 复制行样式
                if worksheet.max_column >= 1:
                    source_cell = worksheet.cell(row=row_num, column=1)
                    target_cell_n = worksheet.cell(row=row_num, column=actual_col_idx)
                    target_cell_o = worksheet.cell(row=row_num, column=overtime_col_idx)

                    # 复制样式，为超时情况应用黄色背景
                    copy_cell_style(source_cell, target_cell_n, is_overtime_positive=False)
                    copy_cell_style(source_cell, target_cell_o, is_overtime_positive=is_overtime)

            # 保存工作簿
            try:
                workbook.save(output_file)
                logging.info(f"文件保存成功: {output_file}")
            except PermissionError as e:
                error_msg = f"文件保存失败，可能文件正在被其他程序使用: {output_file}"
                logging.error(error_msg)
                # 尝试保存到不同的文件名
                import time
                timestamp = int(time.time())
                backup_file = output_file.replace('.xlsx', f'_backup_{timestamp}.xlsx')
                try:
                    workbook.save(backup_file)
                    logging.info(f"已保存到备份文件: {backup_file}")
                    output_file = backup_file
                except Exception as backup_error:
                    logging.error(f"备份保存也失败: {backup_error}")
                    raise PermissionError(f"无法保存文件，请关闭Excel后重试: {output_file}")
            except Exception as e:
                logging.error(f"文件保存失败: {e}")
                raise

            # 统计信息
            total_rows = len(df)
            processed_rows = df['实际作业时长（不含午休，休息，晚餐时长）'].notna().sum()
            # 修改超时统计逻辑，因为现在是字符串格式
            overtime_count = 0
            for val in df['超作业标准工时(分)']:
                if pd.notna(val) and isinstance(val, str) and not val.startswith('-') and val != '0:00':
                    overtime_count += 1

            logging.info(f"处理统计: 总行数 {total_rows}, 成功处理 {processed_rows} 行, 超时作业 {overtime_count} 个")

            return output_file

        except Exception as e:
            logging.error(f"实际工时分析失败: {str(e)}")
            raise

    def parse_time_value(self, time_value):
        """解析时间值，返回datetime.time对象"""
        try:
            import pandas as pd
            from datetime import datetime, time

            if pd.isna(time_value):
                return None

            # 如果是datetime对象，提取时间部分
            if isinstance(time_value, datetime):
                return time_value.time()

            # 如果是time对象，直接返回
            if isinstance(time_value, time):
                return time_value

            # 如果是字符串，尝试解析
            if isinstance(time_value, str):
                time_str = time_value.strip()

                # 尝试解析 HH:MM 格式
                if ':' in time_str:
                    parts = time_str.split(':')
                    if len(parts) >= 2:
                        hour = int(parts[0])
                        minute = int(parts[1])
                        return time(hour, minute)

                # 尝试解析纯数字格式（如 830 表示 8:30）
                if time_str.isdigit() and len(time_str) in [3, 4]:
                    if len(time_str) == 3:
                        hour = int(time_str[0])
                        minute = int(time_str[1:])
                    else:
                        hour = int(time_str[:2])
                        minute = int(time_str[2:])
                    return time(hour, minute)

            # 如果是数字（Excel时间格式），转换为时间
            if isinstance(time_value, (int, float)):
                # Excel时间格式：0.5 = 12:00, 1.0 = 24:00
                total_seconds = time_value * 24 * 3600
                hours = int(total_seconds // 3600) % 24
                minutes = int((total_seconds % 3600) // 60)
                return time(hours, minutes)

            return None

        except Exception as e:
            logging.warning(f"时间解析失败: {time_value}, 错误: {str(e)}")
            return None

    def parse_duration_value(self, duration_value):
        """解析时长值，返回分钟数"""
        try:
            import pandas as pd
            from datetime import datetime, time

            if pd.isna(duration_value):
                return None

            # 如果是字符串格式 "H:MM"
            if isinstance(duration_value, str):
                duration_str = duration_value.strip()
                if ':' in duration_str:
                    parts = duration_str.split(':')
                    if len(parts) >= 2:
                        hours = int(parts[0])
                        minutes = int(parts[1])
                        return hours * 60 + minutes

            # 如果是数字（小时数）
            if isinstance(duration_value, (int, float)):
                return int(duration_value * 60)

            # 如果是datetime对象，计算从0点开始的分钟数
            if isinstance(duration_value, datetime):
                return duration_value.hour * 60 + duration_value.minute

            # 如果是time对象
            if isinstance(duration_value, time):
                return duration_value.hour * 60 + duration_value.minute

            return None

        except Exception as e:
            logging.warning(f"时长解析失败: {duration_value}, 错误: {str(e)}")
            return None

    def calculate_actual_working_duration(self, start_time, end_time):
        """计算实际工作时长（扣除休息时间），返回分钟数"""
        try:
            # 将time对象转换为分钟数（从00:00开始）
            start_minutes = start_time.hour * 60 + start_time.minute
            end_minutes = end_time.hour * 60 + end_time.minute

            # 处理跨天情况
            if end_minutes < start_minutes:
                end_minutes += 24 * 60

            # 基础工作时长
            base_duration = end_minutes - start_minutes

            # 定义休息时间段（分钟数表示）
            break_periods = [
                (11 * 60 + 30, 12 * 60 + 30),  # 午休：11:30-12:30 (60分钟)
                (10 * 60, 10 * 60 + 10),       # 上午休息：10:00-10:10 (10分钟)
                (15 * 60, 15 * 60 + 10),       # 下午休息：15:00-15:10 (10分钟)
                (17 * 60 + 30, 18 * 60)        # 晚餐：17:30-18:00 (30分钟)
            ]

            # 计算需要扣除的休息时间
            total_break_minutes = 0
            for break_start, break_end in break_periods:
                overlap = self.calculate_overlap_minutes(start_minutes, end_minutes, break_start, break_end)
                total_break_minutes += overlap

            # 实际工作时长 = 基础时长 - 休息时间
            actual_duration = max(0, base_duration - total_break_minutes)

            return actual_duration

        except Exception as e:
            logging.warning(f"工时计算失败: {start_time} - {end_time}, 错误: {str(e)}")
            return 0

    def calculate_overlap_minutes(self, work_start, work_end, break_start, break_end):
        """计算工作时间与休息时间的重叠分钟数"""
        overlap_start = max(work_start, break_start)
        overlap_end = min(work_end, break_end)

        if overlap_start < overlap_end:
            return overlap_end - overlap_start
        return 0

    def format_duration_to_hm(self, minutes):
        """将分钟数格式化为 H:MM 格式"""
        if minutes is None or minutes < 0:
            return "0:00"

        hours = minutes // 60
        mins = minutes % 60
        return f"{hours}:{mins:02d}"

    def format_overtime_to_hm(self, minutes):
        """将超时分钟数格式化为H:MM格式（可以是负数）"""
        if minutes is None:
            return "0:00"

        if minutes < 0:
            # 负数表示提前完成
            abs_minutes = abs(minutes)
            hours = abs_minutes // 60
            mins = abs_minutes % 60
            return f"-{hours}:{mins:02d}"
        else:
            # 正数表示超时
            hours = minutes // 60
            mins = minutes % 60
            return f"{hours}:{mins:02d}"
    
    def update_analytics(self):
        """更新数据分析"""
        file_path = self.analytics_file_path.get().strip()
        if not file_path:
            messagebox.showerror("错误", "请选择Excel文件")
            return
        
        # 清空日志文本框
        self.analytics_log_text.delete(1.0, tk.END)
        
        # 创建自定义的日志处理器，将日志输出到文本框
        log_handler = TextHandler(self.analytics_log_text)
        log_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        log_handler.setFormatter(formatter)
        
        # 获取根日志记录器并添加处理器
        root_logger = logging.getLogger()
        root_logger.addHandler(log_handler)
        
        # 在新线程中运行处理函数
        thread = threading.Thread(target=self.run_analytics_update, args=(file_path,))
        thread.daemon = True
        thread.start()
    
    def run_analytics_update(self, file_path):
        """在线程中运行数据分析更新"""
        try:
            # 记录开始处理
            logging.info(f"开始处理文件: {file_path}")
            
            # 加载工作簿
            wb = load_workbook(filename=file_path, data_only=True)
            
            # 检查主数据表是否存在
            if 'Sheet1' not in wb.sheetnames:
                error_msg = "错误：找不到主数据表'Sheet1'"
                logging.error(error_msg)
                self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
                return
            
            # 获取主数据表
            main_ws = wb['Sheet1']
            
            # 检查数据表结构是否符合预期
            expected_headers = ['日期', '栋别', '起始时间', '截止时间', '作业时长', 
                              '作业类型', '方向', '作业内容', '数量', '备注',
                              '实际开始时间', '实际结束时间', '实际作业时长', '异常原因']
            
            # 检查标题行
            headers_match = True
            for col, header in enumerate(expected_headers, start=1):
                cell_value = main_ws.cell(row=1, column=col).value
                if cell_value != header:
                    logging.warning(f"警告: 列 {col} 的标题不符合预期。预期: '{header}', 实际: '{cell_value}'")
                    headers_match = False
            
            if not headers_match:
                warning_msg = "警告：表格结构与预期不符，可能导致分析图表不准确。是否继续？"
                continue_processing = messagebox.askyesno("警告", warning_msg)
                if not continue_processing:
                    return
            
            # 创建分析图表
            logging.info("正在更新数据分析图表...")
            create_analysis_charts(wb, 'Sheet1')
            
            # 选择保存位置
            default_output = file_path.rsplit('.', 1)[0] + "_已更新.xlsx"
            output_file = filedialog.asksaveasfilename(
                title="选择保存位置",
                filetypes=[("Excel文件", "*.xlsx")],
                defaultextension=".xlsx",
                initialfile=os.path.basename(default_output)
            )
            
            if not output_file:
                logging.info("用户取消了保存操作")
                self.root.after(0, lambda: messagebox.showinfo("提示", "操作已取消"))
                return
            
            # 保存更改
            wb.save(output_file)
            
            success_msg = f"分析图表已更新，结果已保存到 {output_file}"
            logging.info(success_msg)
            
            # 显示成功消息
            self.root.after(0, lambda: messagebox.showinfo("成功", success_msg))
            
        except Exception as e:
            error_msg = f"更新分析图表时出错: {str(e)}"
            logging.error(error_msg)
            logging.error(traceback.format_exc())
            # 显示错误消息
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))
    
    def generate_mock_data(self):
        """生成模拟测试数据"""
        try:
            # 先选择文件
            file_path = filedialog.askopenfilename(
                title="选择需要填充模拟数据的Excel文件",
                filetypes=[("Excel文件", "*.xlsx"), ("所有文件", "*.*")]
            )
            
            if not file_path:
                return  # 用户取消选择
            
            # 加载工作簿
            wb = load_workbook(filename=file_path)
            
            # 检查主数据表是否存在
            if 'Sheet1' not in wb.sheetnames:
                messagebox.showerror("错误", "找不到主数据表'Sheet1'")
                return
            
            # 获取主数据表
            ws = wb['Sheet1']
            
            # 生成模拟数据
            modified_rows = generate_mock_actual_data(ws)
            
            if modified_rows > 0:
                # 选择保存位置
                default_output = file_path.rsplit('.', 1)[0] + "_模拟数据.xlsx"
                output_file = filedialog.asksaveasfilename(
                    title="选择保存位置",
                    filetypes=[("Excel文件", "*.xlsx")],
                    defaultextension=".xlsx",
                    initialfile=os.path.basename(default_output)
                )
                
                if not output_file:
                    logging.info("用户取消了保存操作")
                    messagebox.showinfo("提示", "操作已取消")
                    return
                
                # 保存文件
                wb.save(output_file)
                messagebox.showinfo("成功", f"已生成 {modified_rows} 行模拟数据，保存到 {output_file}")
            else:
                messagebox.showwarning("提示", "没有生成任何模拟数据。请确保Excel表格包含正确的计划时间数据。")
        
        except Exception as e:
            messagebox.showerror("错误", f"生成模拟数据时出错: {str(e)}")
            logging.error(f"生成模拟数据时出错: {str(e)}")
            logging.error(traceback.format_exc())

    def show_auto_schedule(self):
        """显示自动时间排程页面"""
        # 清除主框架
        for widget in self.main_frame.winfo_children():
            widget.destroy()

        # 创建自动排程页面
        auto_frame = tk.Frame(self.main_frame, bg=BG_COLOR)
        auto_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_frame = tk.Frame(auto_frame, bg=THEME_COLOR)
        title_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = tk.Label(
            title_frame,
            text="⏰ 自动时间排程",
            font=("Arial", 16, "bold"),
            fg=HEADING_COLOR,
            bg=THEME_COLOR,
            padx=10,
            pady=10
        )
        title_label.pack(side=tk.LEFT)

        back_btn = tk.Button(
            title_frame,
            text="🔙 返回",
            command=self.show_welcome_page,
            font=("Arial", 10),
            bg=THEME_COLOR,
            fg=HEADING_COLOR,
            activebackground=BUTTON_COLOR,
            activeforeground=HEADING_COLOR,
            bd=0
        )
        back_btn.pack(side=tk.RIGHT, padx=10)

        # 内容框架
        content_frame = tk.Frame(auto_frame, bg=BG_COLOR)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # 说明文本框架
        desc_frame = tk.Frame(content_frame, bg=BG_COLOR)
        desc_frame.pack(fill=tk.X, pady=(0, 20))

        # 主要说明文本
        description = tk.Label(
            desc_frame,
            text="""智能自动排程功能说明：
• 自动检测表格类型：完整表格直接排程，待填充表格先智能填充再排程
• 智能填充：根据作业内容自动填充作业类型、方向、数量、时长
• A栋、C栋：串行作业，无时间冲突
• B栋：出库口和入库口可并行作业
• 午休时间：11:30-12:30（自动跳过）
• 默认开始时间：08:30
• 截止时间超过17:30的作业将标记为黄色
• 支持精确匹配、模糊匹配和关键词匹配""",
            font=("Arial", 12),
            fg=TEXT_COLOR,
            bg=BG_COLOR,
            justify=tk.LEFT,
            wraplength=800
        )
        description.pack(anchor=tk.W)

        # 特殊提示文本（绿色）
        special_note = tk.Label(
            desc_frame,
            text="• 如遇拼车，请人工手动调整拼车数量，以及作业时间",
            font=("Arial", 12),
            fg="#008000",  # 绿色
            bg=BG_COLOR,
            justify=tk.LEFT,
            wraplength=800
        )
        special_note.pack(anchor=tk.W, pady=(5, 0))

        # 文件选择框架
        file_frame = tk.Frame(content_frame, bg=BG_COLOR)
        file_frame.pack(fill=tk.X, pady=10)

        # 作业计划表文件选择
        plan_label = tk.Label(
            file_frame,
            text="选择作业计划表：",
            font=("Arial", 11),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        plan_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.auto_plan_file_path = tk.StringVar()
        plan_entry = tk.Entry(
            file_frame,
            textvariable=self.auto_plan_file_path,
            font=("Arial", 11),
            width=50
        )
        plan_entry.grid(row=0, column=1, padx=(0, 10))

        plan_browse_btn = tk.Button(
            file_frame,
            text="📁 浏览",
            command=lambda: self.browse_file(self.auto_plan_file_path),
            font=("Arial", 11),
            bg=BUTTON_COLOR,
            fg=HEADING_COLOR
        )
        plan_browse_btn.grid(row=0, column=2)

        # 配置文件选择
        config_label = tk.Label(
            file_frame,
            text="选择时间规划配置：",
            font=("Arial", 11),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        config_label.grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))

        self.auto_config_file_path = tk.StringVar()
        # 设置默认配置文件路径
        self.auto_config_file_path.set("作业类型时间规划.xlsx")

        config_entry = tk.Entry(
            file_frame,
            textvariable=self.auto_config_file_path,
            font=("Arial", 11),
            width=50
        )
        config_entry.grid(row=1, column=1, padx=(0, 10), pady=(10, 0))

        config_browse_btn = tk.Button(
            file_frame,
            text="📁 浏览",
            command=lambda: self.browse_file(self.auto_config_file_path),
            font=("Arial", 11),
            bg=BUTTON_COLOR,
            fg=HEADING_COLOR
        )
        config_browse_btn.grid(row=1, column=2, pady=(10, 0))

        # 配置选项框架
        options_frame = tk.Frame(content_frame, bg=BG_COLOR)
        options_frame.pack(fill=tk.X, pady=20)

        # 开始时间设置
        start_time_label = tk.Label(
            options_frame,
            text="开始时间：",
            font=("Arial", 11),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        start_time_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.auto_start_time = tk.StringVar(value="08:30")
        start_time_entry = tk.Entry(
            options_frame,
            textvariable=self.auto_start_time,
            font=("Arial", 11),
            width=10
        )
        start_time_entry.grid(row=0, column=1, padx=(0, 20))

        # 栋别选择
        buildings_label = tk.Label(
            options_frame,
            text="处理栋别：",
            font=("Arial", 11),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        buildings_label.grid(row=0, column=2, sticky=tk.W, padx=(0, 10))

        # 栋别复选框
        self.building_vars = {}
        buildings = ['A', 'B', 'C']
        for i, building in enumerate(buildings):
            var = tk.BooleanVar(value=True)
            self.building_vars[building] = var
            cb = tk.Checkbutton(
                options_frame,
                text=f"{building}栋",
                variable=var,
                font=("Arial", 11),
                fg=TEXT_COLOR,
                bg=BG_COLOR,
                selectcolor=BG_COLOR
            )
            cb.grid(row=0, column=3+i, padx=5)

        # 按钮框架
        button_frame = tk.Frame(content_frame, bg=BG_COLOR)
        button_frame.pack(pady=20)

        # 智能推荐按钮
        recommend_btn = tk.Button(
            button_frame,
            text="🧠 智能推荐方案",
            command=self.generate_recommendations,
            font=("Arial", 12, "bold"),
            bg=WARNING_COLOR,
            fg=HEADING_COLOR,
            padx=20,
            pady=10
        )
        recommend_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 处理按钮
        process_btn = tk.Button(
            button_frame,
            text="🤖 开始智能排程",
            command=self.process_auto_schedule,
            font=("Arial", 12, "bold"),
            bg=BUTTON_COLOR,
            fg=HEADING_COLOR,
            padx=20,
            pady=10
        )
        process_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 评估现有排程按钮
        evaluate_btn = tk.Button(
            button_frame,
            text="📊 评估现有排程",
            command=self.evaluate_current_schedule,
            font=("Arial", 12, "bold"),
            bg=SUCCESS_COLOR,
            fg=HEADING_COLOR,
            padx=20,
            pady=10
        )
        evaluate_btn.pack(side=tk.LEFT)

        # 日志显示区域
        log_frame = tk.Frame(content_frame, bg=BG_COLOR)
        log_frame.pack(fill=tk.X, expand=False, pady=(20, 0))

        log_label = tk.Label(
            log_frame,
            text="处理日志：",
            font=("Arial", 11, "bold"),
            fg=TEXT_COLOR,
            bg=BG_COLOR
        )
        log_label.pack(anchor=tk.W)

        # 创建日志文本框和滚动条
        log_text_frame = tk.Frame(log_frame, bg=BG_COLOR)
        log_text_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        self.auto_log_text = tk.Text(
            log_text_frame,
            font=("Consolas", 9),
            bg="white",
            fg="black",
            state=tk.DISABLED,
            wrap=tk.WORD,
            height=12,  # 减少高度为推荐方案留空间
            width=100   # 增加宽度
        )

        log_scrollbar = tk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.auto_log_text.yview)
        self.auto_log_text.configure(yscrollcommand=log_scrollbar.set)

        self.auto_log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 推荐方案显示区域
        self.create_recommendations_display(content_frame)

    def process_auto_schedule(self):
        """处理智能自动排程"""
        plan_file = self.auto_plan_file_path.get().strip()
        config_file = self.auto_config_file_path.get().strip()
        start_time = self.auto_start_time.get().strip()

        if not plan_file:
            messagebox.showerror("错误", "请选择作业计划表文件")
            return

        if not config_file:
            messagebox.showerror("错误", "请选择时间规划配置文件")
            return

        # 获取选中的栋别
        selected_buildings = [building for building, var in self.building_vars.items() if var.get()]
        if not selected_buildings:
            messagebox.showerror("错误", "请至少选择一个栋别")
            return

        # 清空日志文本框
        self.auto_log_text.delete(1.0, tk.END)

        # 创建自定义的日志处理器，将日志输出到文本框
        log_handler = TextHandler(self.auto_log_text)
        log_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        log_handler.setFormatter(formatter)

        # 获取根日志记录器并添加处理器
        root_logger = logging.getLogger()
        root_logger.addHandler(log_handler)

        # 在新线程中运行处理函数
        thread = threading.Thread(
            target=self.run_auto_schedule,
            args=(plan_file, config_file, start_time, selected_buildings)
        )
        thread.daemon = True
        thread.start()

    def run_auto_schedule(self, plan_file, config_file, start_time, selected_buildings):
        """在线程中运行自动排程"""
        try:
            # 记录开始处理
            logging.info(f"开始自动排程处理")
            logging.info(f"作业计划表: {plan_file}")
            logging.info(f"配置文件: {config_file}")
            logging.info(f"开始时间: {start_time}")
            logging.info(f"处理栋别: {', '.join(selected_buildings)}")

            # 调用自动排程函数
            result = auto_fill_work_schedule(
                excel_file_path=plan_file,
                config_file_path=config_file,
                start_time=start_time,
                buildings=selected_buildings
            )

            if result['success']:
                success_msg = result['message']
                output_file = result.get('output_file', '')
                logging.info(success_msg)

                # 显示成功消息并询问是否打开编辑器
                def show_success_and_ask():
                    messagebox.showinfo("成功", success_msg)
                    if output_file and messagebox.askyesno(
                        "打开编辑器",
                        "自动排程已完成！\n\n是否打开排程编辑器进行手动调整？"
                    ):
                        self.open_editor_with_file(output_file)

                self.root.after(0, show_success_and_ask)
            else:
                error_msg = result['error']
                logging.error(error_msg)
                # 显示错误消息
                self.root.after(0, lambda: messagebox.showerror("错误", error_msg))

        except Exception as e:
            error_msg = f"自动排程处理失败: {str(e)}"
            logging.error(error_msg)
            logging.error(traceback.format_exc())
            # 显示错误消息
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))

    def create_recommendations_display(self, parent):
        """创建推荐方案显示区域"""
        # 推荐方案框架（初始隐藏）
        self.recommendations_frame = tk.Frame(parent, bg="#e8f4fd", relief=tk.RAISED, bd=3)

        # 推荐方案标题
        rec_title = tk.Label(
            self.recommendations_frame,
            text="🧠 智能推荐方案",
            font=("Arial", 16, "bold"),
            fg="#0066cc",
            bg="#e8f4fd"
        )
        rec_title.pack(pady=(15, 10))

        # 推荐方案列表框架
        rec_list_frame = tk.Frame(self.recommendations_frame, bg="#e8f4fd")
        rec_list_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 10))

        # 创建Treeview显示推荐方案
        columns = ('rank', 'name', 'score', 'grade', 'description')
        self.recommendations_tree = ttk.Treeview(rec_list_frame, columns=columns, show='headings', height=8)

        # 设置列标题
        self.recommendations_tree.heading('rank', text='排名')
        self.recommendations_tree.heading('name', text='方案名称')
        self.recommendations_tree.heading('score', text='评分')
        self.recommendations_tree.heading('grade', text='等级')
        self.recommendations_tree.heading('description', text='方案描述')

        # 设置列宽
        self.recommendations_tree.column('rank', width=60, anchor='center')
        self.recommendations_tree.column('name', width=120, anchor='center')
        self.recommendations_tree.column('score', width=80, anchor='center')
        self.recommendations_tree.column('grade', width=60, anchor='center')
        self.recommendations_tree.column('description', width=400, anchor='w')

        # 添加滚动条
        rec_scrollbar = ttk.Scrollbar(rec_list_frame, orient=tk.VERTICAL, command=self.recommendations_tree.yview)
        self.recommendations_tree.configure(yscrollcommand=rec_scrollbar.set)

        self.recommendations_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        rec_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 推荐方案操作按钮
        rec_button_frame = tk.Frame(self.recommendations_frame, bg="#e8f4fd")
        rec_button_frame.pack(pady=(0, 15))

        apply_btn = tk.Button(
            rec_button_frame,
            text="✅ 应用选中方案",
            command=self.apply_selected_recommendation_from_tree,
            font=("Arial", 11, "bold"),
            bg=SUCCESS_COLOR,
            fg=HEADING_COLOR,
            padx=15,
            pady=5
        )
        apply_btn.pack(side=tk.LEFT, padx=(0, 10))

        detail_btn = tk.Button(
            rec_button_frame,
            text="📋 查看详情",
            command=self.show_recommendation_detail_from_tree,
            font=("Arial", 11, "bold"),
            bg=THEME_COLOR,
            fg=HEADING_COLOR,
            padx=15,
            pady=5
        )
        detail_btn.pack(side=tk.LEFT, padx=(0, 10))

        hide_btn = tk.Button(
            rec_button_frame,
            text="❌ 隐藏推荐",
            command=self.hide_recommendations,
            font=("Arial", 11),
            bg=SECONDARY_COLOR,
            fg=HEADING_COLOR,
            padx=15,
            pady=5
        )
        hide_btn.pack(side=tk.LEFT)

        # 存储推荐数据
        self.current_recommendations = []

    def apply_selected_recommendation_from_tree(self):
        """从树形控件应用选中的推荐方案"""
        selection = self.recommendations_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个推荐方案")
            return

        # 获取选中项的索引
        item = selection[0]
        item_index = self.recommendations_tree.index(item)

        if item_index >= len(self.current_recommendations):
            messagebox.showerror("错误", "选中的推荐方案无效")
            return

        selected_recommendation = self.current_recommendations[item_index]
        plan_file = self.auto_plan_file_path.get().strip()

        # 确认应用
        confirm = messagebox.askyesno(
            "确认应用",
            f"确定要应用推荐方案 '{selected_recommendation['name']}' 吗？\n"
            f"评分: {selected_recommendation['total_score']:.1f} ({selected_recommendation['grade']})\n"
            f"这将创建一个新的Excel文件。"
        )

        if not confirm:
            return

        # 应用推荐方案
        self.apply_recommendation(selected_recommendation)

    def show_recommendation_detail_from_tree(self):
        """从树形控件显示选中推荐方案的详情"""
        selection = self.recommendations_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个推荐方案")
            return

        # 获取选中项的索引
        item = selection[0]
        item_index = self.recommendations_tree.index(item)

        if item_index >= len(self.current_recommendations):
            messagebox.showerror("错误", "选中的推荐方案无效")
            return

        selected_recommendation = self.current_recommendations[item_index]

        # 创建详情窗口
        self.show_recommendation_detail_window(selected_recommendation, self.root)

    def generate_recommendations(self):
        """生成智能推荐方案"""
        plan_file = self.auto_plan_file_path.get().strip()
        config_file = self.auto_config_file_path.get().strip()
        start_time = self.auto_start_time.get().strip()

        if not plan_file:
            messagebox.showerror("错误", "请选择作业计划表文件")
            return

        if not config_file:
            messagebox.showerror("错误", "请选择时间规划配置文件")
            return

        # 获取选中的栋别
        selected_buildings = [building for building, var in self.building_vars.items() if var.get()]
        if not selected_buildings:
            messagebox.showerror("错误", "请至少选择一个栋别")
            return

        # 清空日志
        self.auto_log_text.delete(1.0, tk.END)

        # 在新线程中生成推荐
        thread = threading.Thread(
            target=self.run_generate_recommendations,
            args=(plan_file, config_file, start_time, selected_buildings)
        )
        thread.daemon = True
        thread.start()

    def run_generate_recommendations(self, plan_file, config_file, start_time, selected_buildings):
        """在线程中运行推荐生成"""
        try:
            logging.info("开始生成智能推荐方案...")

            # 生成推荐方案
            result = generate_smart_recommendations(
                excel_file_path=plan_file,
                config_file_path=config_file,
                start_time=start_time,
                buildings=selected_buildings
            )

            if result['success']:
                self.current_recommendations = result['recommendations']
                logging.info(f"获取到 {len(self.current_recommendations)} 个推荐方案")

                success_msg = f"成功生成 {len(self.current_recommendations)} 个推荐方案"
                logging.info(success_msg)

                # 显示推荐方案选择窗口
                self.root.after(0, self.show_recommendations_window)

                success_msg = f"成功生成 {len(self.current_recommendations)} 个推荐方案\n\n请在弹出窗口中选择最适合的方案"
                self.root.after(100, lambda: messagebox.showinfo("成功", success_msg))

            else:
                error_msg = result['error']
                logging.error(error_msg)
                self.root.after(0, lambda: messagebox.showerror("错误", error_msg))

        except Exception as e:
            error_msg = f"推荐生成失败: {str(e)}"
            logging.error(error_msg)
            logging.error(traceback.format_exc())
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))

    def show_recommendations_window(self):
        """显示推荐方案选择窗口"""
        try:
            logging.info(f"打开推荐方案选择窗口，共 {len(self.current_recommendations)} 个方案")

            # 创建推荐方案选择窗口
            rec_window = tk.Toplevel(self.root)
            rec_window.title("🧠 智能推荐方案选择")
            rec_window.geometry("1000x700")
            rec_window.configure(bg="#f0f8ff")
            rec_window.resizable(True, True)

            # 设置窗口图标和置顶
            rec_window.transient(self.root)
            rec_window.grab_set()

            # 窗口居中
            rec_window.update_idletasks()
            x = (rec_window.winfo_screenwidth() // 2) - (1000 // 2)
            y = (rec_window.winfo_screenheight() // 2) - (700 // 2)
            rec_window.geometry(f"1000x700+{x}+{y}")

            # 标题区域
            title_frame = tk.Frame(rec_window, bg="#4a90e2", height=80)
            title_frame.pack(fill=tk.X)
            title_frame.pack_propagate(False)

            title_label = tk.Label(
                title_frame,
                text="🧠 智能推荐方案选择",
                font=("Arial", 18, "bold"),
                fg="white",
                bg="#4a90e2"
            )
            title_label.pack(expand=True)

            subtitle_label = tk.Label(
                title_frame,
                text=f"系统为您生成了 {len(self.current_recommendations)} 个优化方案，请选择最适合的方案",
                font=("Arial", 11),
                fg="#e6f3ff",
                bg="#4a90e2"
            )
            subtitle_label.pack()

            # 主内容区域
            content_frame = tk.Frame(rec_window, bg="#f0f8ff")
            content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # 推荐方案列表
            list_frame = tk.Frame(content_frame, bg="#f0f8ff")
            list_frame.pack(fill=tk.BOTH, expand=True)

            # 创建Treeview显示推荐方案
            columns = ('rank', 'name', 'score', 'grade', 'description')
            rec_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)

            # 设置列标题
            rec_tree.heading('rank', text='排名')
            rec_tree.heading('name', text='方案名称')
            rec_tree.heading('score', text='评分')
            rec_tree.heading('grade', text='等级')
            rec_tree.heading('description', text='方案描述')

            # 设置列宽
            rec_tree.column('rank', width=60, anchor='center')
            rec_tree.column('name', width=150, anchor='center')
            rec_tree.column('score', width=80, anchor='center')
            rec_tree.column('grade', width=60, anchor='center')
            rec_tree.column('description', width=500, anchor='w')

            # 添加滚动条
            scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=rec_tree.yview)
            rec_tree.configure(yscrollcommand=scrollbar.set)

            rec_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 添加推荐方案数据
            for i, rec in enumerate(self.current_recommendations):
                rec_tree.insert('', 'end', values=(
                    rec['rank'],
                    rec['name'],
                    f"{rec['total_score']:.1f}",
                    rec['grade'],
                    rec['description']
                ))

            # 选中第一个推荐方案
            if self.current_recommendations:
                first_item = rec_tree.get_children()[0]
                rec_tree.selection_set(first_item)
                rec_tree.focus(first_item)

            # 按钮区域 - 固定在窗口底部，增加高度确保按钮完整显示
            button_container = tk.Frame(rec_window, bg="#f0f8ff", height=100)
            button_container.pack(side=tk.BOTTOM, fill=tk.X, padx=20, pady=(10, 20))
            button_container.pack_propagate(False)

            button_frame = tk.Frame(button_container, bg="#f0f8ff")
            button_frame.pack(expand=True, pady=10)

            # 应用选中方案按钮
            def apply_selected():
                selection = rec_tree.selection()
                if not selection:
                    messagebox.showwarning("警告", "请先选择一个推荐方案")
                    return

                item_index = rec_tree.index(selection[0])
                selected_rec = self.current_recommendations[item_index]

                # 确认应用
                confirm = messagebox.askyesno(
                    "确认应用",
                    f"确定要应用推荐方案 '{selected_rec['name']}' 吗？\n\n"
                    f"📊 评分: {selected_rec['total_score']:.1f}/100 ({selected_rec['grade']})\n"
                    f"📝 描述: {selected_rec['description']}\n\n"
                    f"这将创建一个新的Excel文件。",
                    parent=rec_window
                )

                if confirm:
                    rec_window.destroy()
                    self.apply_recommendation(selected_rec)

            # 查看详情按钮
            def show_details():
                selection = rec_tree.selection()
                if not selection:
                    messagebox.showwarning("警告", "请先选择一个推荐方案")
                    return

                item_index = rec_tree.index(selection[0])
                selected_rec = self.current_recommendations[item_index]
                self.show_recommendation_detail_window(selected_rec, rec_window)

            apply_btn = tk.Button(
                button_frame,
                text="✅ 应用选中方案",
                command=apply_selected,
                font=("Arial", 11, "bold"),
                bg="#28a745",
                fg="white",
                padx=15,
                pady=6
            )
            apply_btn.pack(side=tk.LEFT, padx=(0, 12))

            detail_btn = tk.Button(
                button_frame,
                text="📋 查看详情",
                command=show_details,
                font=("Arial", 11, "bold"),
                bg="#007bff",
                fg="white",
                padx=15,
                pady=6
            )
            detail_btn.pack(side=tk.LEFT, padx=(0, 12))

            # 下载详细文档按钮
            def download_detailed_doc():
                self.download_smart_recommendation_doc(rec_window)

            download_doc_btn = tk.Button(
                button_frame,
                text="📥 下载详细文档",
                command=download_detailed_doc,
                font=("Arial", 11, "bold"),
                bg="#17a2b8",
                fg="white",
                padx=15,
                pady=6
            )
            download_doc_btn.pack(side=tk.LEFT, padx=(0, 12))

            cancel_btn = tk.Button(
                button_frame,
                text="❌ 取消",
                command=rec_window.destroy,
                font=("Arial", 11),
                bg="#6c757d",
                fg="white",
                padx=15,
                pady=6
            )
            cancel_btn.pack(side=tk.LEFT)

            logging.info("推荐方案选择窗口已打开")

        except Exception as e:
            logging.error(f"显示推荐方案窗口失败: {str(e)}")
            logging.error(traceback.format_exc())

    def apply_recommendation(self, recommendation):
        """应用推荐方案"""
        plan_file = self.auto_plan_file_path.get().strip()

        # 在新线程中应用推荐方案
        thread = threading.Thread(
            target=self.run_apply_recommendation,
            args=(plan_file, recommendation)
        )
        thread.daemon = True
        thread.start()

    def run_apply_recommendation(self, plan_file, recommendation):
        """在线程中应用推荐方案"""
        try:
            logging.info(f"开始应用推荐方案: {recommendation['name']}")

            result = apply_recommended_schedule(plan_file, recommendation)

            if result['success']:
                success_msg = result['message']
                logging.info(success_msg)
                self.root.after(0, lambda: messagebox.showinfo("成功", success_msg))
            else:
                error_msg = result['error']
                logging.error(error_msg)
                self.root.after(0, lambda: messagebox.showerror("错误", error_msg))

        except Exception as e:
            error_msg = f"应用推荐方案失败: {str(e)}"
            logging.error(error_msg)
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))

    def show_recommendation_detail_window(self, recommendation, parent_window):
        """显示推荐方案详情窗口"""
        score_result = recommendation['score_result']

        # 创建详情窗口
        detail_window = tk.Toplevel(parent_window)
        detail_window.title(f"📊 推荐方案详情 - {recommendation['name']}")
        detail_window.geometry("700x600")
        detail_window.configure(bg="#f8f9fa")
        detail_window.transient(parent_window)
        detail_window.grab_set()

        # 窗口居中
        detail_window.update_idletasks()
        x = (detail_window.winfo_screenwidth() // 2) - (700 // 2)
        y = (detail_window.winfo_screenheight() // 2) - (600 // 2)
        detail_window.geometry(f"700x600+{x}+{y}")

        # 标题区域
        title_frame = tk.Frame(detail_window, bg="#28a745", height=60)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)

        title_label = tk.Label(
            title_frame,
            text=f"📊 {recommendation['name']} - 详细分析",
            font=("Arial", 16, "bold"),
            fg="white",
            bg="#28a745"
        )
        title_label.pack(expand=True)

        # 评分概览
        score_frame = tk.Frame(detail_window, bg="#e8f5e8", relief=tk.RAISED, bd=2)
        score_frame.pack(fill=tk.X, padx=20, pady=(20, 10))

        score_info = tk.Label(
            score_frame,
            text=f"🎯 综合评分: {recommendation['total_score']:.1f}/100 ({recommendation['grade']})",
            font=("Arial", 14, "bold"),
            fg="#155724",
            bg="#e8f5e8"
        )
        score_info.pack(pady=10)

        # 详情内容
        content_frame = tk.Frame(detail_window, bg="#f8f9fa")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        detail_text = tk.Text(
            content_frame,
            font=("Consolas", 10),
            bg="white",
            fg="black",
            wrap=tk.WORD,
            padx=15,
            pady=15
        )

        # 添加滚动条
        detail_scrollbar = tk.Scrollbar(content_frame, orient=tk.VERTICAL, command=detail_text.yview)
        detail_text.configure(yscrollcommand=detail_scrollbar.set)

        detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 插入详情内容
        detail_content = f"""📋 方案描述:
{recommendation['description']}

📊 评估报告:
{score_result['report']}

🔍 改进建议:
{chr(10).join('• ' + rec for rec in score_result['recommendations'])}

📈 详细评分:
"""
        for metric, score in score_result['scores'].items():
            detail_content += f"  • {metric}: {score:.1f}/100\n"

        detail_text.insert(tk.END, detail_content)
        detail_text.configure(state=tk.DISABLED)

        # 按钮区域
        button_frame = tk.Frame(detail_window, bg="#f8f9fa")
        button_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        close_btn = tk.Button(
            button_frame,
            text="✅ 关闭",
            command=detail_window.destroy,
            font=("Arial", 12, "bold"),
            bg="#6c757d",
            fg="white",
            padx=30,
            pady=8
        )
        close_btn.pack(side=tk.RIGHT)

    def hide_recommendations(self):
        """隐藏推荐方案区域"""
        self.recommendations_frame.pack_forget()

    def download_smart_recommendation_doc(self, parent_window=None):
        """下载智能推荐系统详细文档"""
        try:
            doc_file = "智能推荐系统详细文档.md"

            # 检查文档文件是否存在
            if not os.path.exists(doc_file):
                messagebox.showerror("错误", f"文档文件 {doc_file} 不存在")
                return

            # 选择保存位置
            save_path = filedialog.asksaveasfilename(
                title="保存智能推荐系统详细文档",
                defaultextension=".md",
                filetypes=[
                    ("Markdown文件", "*.md"),
                    ("所有文件", "*.*")
                ],
                initialfile="智能推荐系统详细文档.md"
            )

            if save_path:
                # 复制文件到选择的位置
                import shutil
                shutil.copy2(doc_file, save_path)

                # 显示成功消息
                success_msg = f"智能推荐系统详细文档已保存到:\n{save_path}"
                messagebox.showinfo("下载成功", success_msg)

                # 询问是否打开文件
                if messagebox.askyesno("打开文件", "是否要打开下载的文档？"):
                    try:
                        if os.name == 'nt':  # Windows
                            os.startfile(save_path)
                        elif os.name == 'posix':  # macOS and Linux
                            subprocess.run(['open', save_path], check=True)
                    except Exception as e:
                        logging.warning(f"无法自动打开文件: {str(e)}")
                        messagebox.showinfo("提示", f"文档已保存，请手动打开:\n{save_path}")

                logging.info(f"智能推荐系统详细文档已下载到: {save_path}")

        except Exception as e:
            error_msg = f"下载文档失败: {str(e)}"
            logging.error(error_msg)
            messagebox.showerror("错误", error_msg)

    def evaluate_current_schedule(self):
        """评估当前排程方案"""
        plan_file = self.auto_plan_file_path.get().strip()
        config_file = self.auto_config_file_path.get().strip()

        if not plan_file:
            messagebox.showerror("错误", "请选择作业计划表文件")
            return

        if not config_file:
            messagebox.showerror("错误", "请选择时间规划配置文件")
            return

        # 清空日志
        self.auto_log_text.delete(1.0, tk.END)

        # 在新线程中评估
        thread = threading.Thread(
            target=self.run_evaluate_schedule,
            args=(plan_file, config_file)
        )
        thread.daemon = True
        thread.start()

    def run_evaluate_schedule(self, plan_file, config_file):
        """在线程中运行排程评估"""
        try:
            logging.info("开始评估现有排程方案...")

            result = evaluate_existing_schedule(plan_file, config_file)

            if result['success']:
                evaluation = result['evaluation']

                # 显示评估结果
                eval_msg = f"""
📊 排程评估结果

🎯 综合评分: {evaluation['total_score']:.1f}/100 ({evaluation['grade']})

📈 各维度评分:
• 时间效率: {evaluation['scores']['time_efficiency']:.1f}/100
• 资源利用率: {evaluation['scores']['resource_utilization']:.1f}/100
• 冲突控制: {evaluation['scores']['conflict_penalty']:.1f}/100
• 超时控制: {evaluation['scores']['overtime_penalty']:.1f}/100
• 负载均衡: {evaluation['scores']['workload_balance']:.1f}/100
• 午休优化: {evaluation['scores']['lunch_optimization']:.1f}/100

💡 改进建议:
{chr(10).join('• ' + rec for rec in evaluation['recommendations'])}
"""

                logging.info(f"评估完成，总分: {evaluation['total_score']:.1f}")

                # 在主线程中显示结果
                self.root.after(0, lambda: messagebox.showinfo("评估结果", eval_msg))

            else:
                error_msg = result['error']
                logging.error(error_msg)
                self.root.after(0, lambda: messagebox.showerror("错误", error_msg))

        except Exception as e:
            error_msg = f"排程评估失败: {str(e)}"
            logging.error(error_msg)
            self.root.after(0, lambda: messagebox.showerror("错误", error_msg))


# 自定义日志处理器，将日志输出到文本框
class TextHandler(logging.Handler):
    def __init__(self, text_widget):
        logging.Handler.__init__(self)
        self.text_widget = text_widget
        
    def emit(self, record):
        msg = self.format(record)
        
        def append():
            self.text_widget.configure(state='normal')
            self.text_widget.insert(tk.END, msg + '\n')
            self.text_widget.configure(state='disabled')
            self.text_widget.yview(tk.END)  # 自动滚动到最新的日志
        
        # 在主线程中更新UI
        self.text_widget.after(0, append)


# 主函数
def main():
    root = tk.Tk()
    app = WorkingScheduleApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()